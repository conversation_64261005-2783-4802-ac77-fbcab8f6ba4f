import { useEffect, useMemo, useState } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { store } from "../../../store/store";

import useSWR from "swr";
import moment from "moment";
import dayjs from "dayjs";

import { Row, Col, Table, Card, Typography } from "antd";

import { getFileDate } from "../controllers/getFileDate";

import { getFileType } from "../controllers/getFileType";
import { getFileCategory } from "../controllers/getFileCategory";
import * as controller from "../../../controllers/files/filesController";

import { Counter } from "../../../components/Counter";
import { SearchInput } from "../../../components/SearchInput";
import { dynamoGetById } from "../../../service/apiDsmDynamo";

import { Date as DatePicker } from "./Date";
import { Actions } from "./Actions";
import { FilterOptions } from "./FilterOptions";
import { AddTagsModal } from "./Modals/AddTagsModal/AddTagsModal";
import { filterByCategory } from "../controllers/filterByCategory";
import { filterByMonth } from "../controllers/filterByMonth";
import { filterBySearch } from "../controllers/filterBySearch";

import { isValid } from "date-fns";

const { Text, Title } = Typography;

export const FilesCard = () => {
  const allFiles = useSelector((state) => state.files.files, shallowEqual);
  const actionsState = useSelector(
    (state) => state.files.filterState,
    shallowEqual
  );

  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [filterDate, setFilterDate] = useState({ start: "", end: "" });

  const dateRange = useSelector((state) => state.files.dateRange, shallowEqual);

  const permissionsData = useSWR("files", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      return [...data.permissions.find((x) => x.page === "Arquivo").actions];
    } catch (error) {
      return [
        { code: "view_files" },
        { code: "upload_file" },
        { code: "download_file" },
        { code: "delete_file" },
        { code: "view_file_details" },
        { code: "manage_file_permissions" },
        { code: "edit_tags" },
        { code: "send_email" },
        { code: "edit_file" }
      ];
    }
  });

  const showEditTags = useMemo(() => {
    let showTags = false;
    if (
      permissionsData?.data
        ?.map((p) => {
          return p.code;
        })
        .includes("edit_tags")
    ) {
      showTags = true;
    }

    return showTags;
  }, [permissionsData]);

  const filterDateSelect = (date) => {
    setFilterDate(date);
  };

  useEffect(() => {
    getAllFiles();
  }, []);

  async function getAllFiles() {
    const { files } = store.getState().files;

    controller.getTags();

    if (files.length > 0) return await controller.getFiles();

    setLoading(true);
    await controller.getFiles();
    setLoading(false);
  }

  const columns = [
    {
      title: "Nome arquivo",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.name.localeCompare(b?.name),
    },
    {
      title: "Tipo do arquivo",
      dataIndex: "type",
      key: "type",
      align: "center",
      sorter: (a, b) => getFileType(a).localeCompare(getFileType(b)),
      render: (id, item) => {
        return <Text>{getFileType(item)}</Text>;
      },
    },
    {
      title: "Cliente",
      dataIndex: "customerName",
      key: "customerName",
      sorter: (a, b) => a.customerName.localeCompare(b.customerName),
      render: (id, item) => {
        return <Text>{item.customerName}</Text>;
      },
    },
    {
      title: "Tags",
      dataIndex: "tags",
      key: "tags",
      sorter: (a, b) =>
        controller
          .getFileTags(a)
          .join(", ")
          .localeCompare(controller.getFileTags(b).join(", ")),
      render: (id, item) => {
        const tagsStr = controller.getFileTags(item).join(", ");
        return <Text key={id}>{tagsStr}</Text>;
      },
    },
    {
      title: "Categoria",
      dataIndex: "category",
      key: "category",
      sorter: (a, b) => getFileCategory(a).localeCompare(getFileCategory(b)),
      render: (id, item) => getFileCategory(item),
    },
    {
      title: "Data",
      dataIndex: "createdAt",
      key: "createdAt",
      sorter: (a, b) =>
        isValid(new Date(a?.createdAt))
          ? new Date(a?.createdAt) - new Date(b?.createdAt)
          : "",
      render: (id, item) => {
        return (
          <Row justify="center" key={id}>
            <Text>{getFileDate(item)}</Text>
          </Row>
        );
      },
    },
    {
      title: "Ações",
      dataIndex: "actions",
      key: "actions",
      align: "center",
      render: (id, item) => {
        return <Actions key={id} item={item} />;
      },
    },
  ];

  const tableData = useMemo(() => {
    if (
      permissionsData?.data
        ?.map((p) => {
          return p.code;
        })
        .includes("view_files")
    ) {
      let filteredData = allFiles;
      if (search !== "") {
        filteredData = filterBySearch(allFiles, search);
      }
      filteredData = filterByCategory(filteredData, actionsState);
      filteredData = filterByMonth(filteredData, dateRange);
      return filteredData;
    } else {
      return [];
    }
  }, [allFiles, search, actionsState, dateRange]);

  return (
    <>
      <Card
        style={{
          boxShadow: "0 0 10px rgba(0,0,0,0.1)",
          borderRadius: "20px",
        }}
      >
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: "1rem" }}
          gutter={[8, 8]}
        >
          <Col xl={3} md={24} sm={24}>
            <Title level={3} style={{ fontWeight: "bold" }}>
              Arquivos
            </Title>
          </Col>
          <Col xl={5} md={24} sm={24}>
            <SearchInput
              placeholder="Faça uma busca..."
              onChange={(e) => setSearch(e)}
            />
          </Col>
          <Col xl={3} md={12} sm={12}>
            {showEditTags ? (
              <>
                <Row justify={"center"}>
                  <AddTagsModal />
                </Row>
              </>
            ) : null}
          </Col>
          <Col xl={7} md={12} sm={12}>
            <FilterOptions />
          </Col>
          <Col xl={6} md={24} sm={24}>
            <DatePicker
              filterDate={filterDate}
              filterDateSelect={filterDateSelect}
            />
          </Col>
        </Row>

        <Row justify={"space-between"}>
          {dateRange !== "" ? (
            <Text strong>
              Arquivos entre{" "}
              {dayjs(dateRange).startOf("month").format("DD/MM/YYYY")} e{" "}
              {dayjs(dateRange).endOf("month").format("DD/MM/YYYY")}
            </Text>
          ) : null}
          <Counter tableData={tableData} />
        </Row>
        <Row style={{ marginTop: "10px" }}>
          <Col span={24}>
            <Table
              scroll={{ x: "100%" }}
              loading={loading}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "30", "50", "100"],
              }}
              dataSource={tableData}
              columns={columns}
            />
          </Col>
        </Row>
      </Card>
    </>
  );
};
