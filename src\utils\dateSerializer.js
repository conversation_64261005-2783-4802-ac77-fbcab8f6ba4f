import moment from 'moment';
import dayjs from 'dayjs';

/**
 * Utilities for serializing/deserializing dates in Redux store
 * Prevents non-serializable data errors
 */

/**
 * Serialize a date object to string for Redux store
 */
export const serializeDate = (date) => {
  if (!date) return null;

  // Handle moment objects
  if (moment.isMoment(date)) {
    return date.format('YYYY-MM-DD HH:mm:ss');
  }

  // Handle dayjs objects
  if (date && typeof date === 'object' && date.isDayjsObject) {
    return date.format('YYYY-MM-DD HH:mm:ss');
  }

  // Handle Date objects
  if (date instanceof Date) {
    return moment(date).format('YYYY-MM-DD HH:mm:ss');
  }

  // Handle string dates
  if (typeof date === 'string') {
    const momentDate = moment(date);
    return momentDate.isValid() ? momentDate.format('YYYY-MM-DD HH:mm:ss') : null;
  }

  return null;
};

/**
 * Serialize a date object to ISO string
 */
export const serializeDateISO = (date) => {
  if (!date) return null;
  
  if (moment.isMoment(date)) {
    return date.toISOString();
  }
  
  if (date instanceof Date) {
    return date.toISOString();
  }
  
  if (typeof date === 'string') {
    const momentDate = moment(date);
    return momentDate.isValid() ? momentDate.toISOString() : null;
  }
  
  return null;
};

/**
 * Deserialize a date string back to moment object
 */
export const deserializeDate = (dateString) => {
  if (!dateString) return null;
  
  const momentDate = moment(dateString);
  return momentDate.isValid() ? momentDate : null;
};

/**
 * Serialize date range object
 */
export const serializeDateRange = (dateRange) => {
  if (!dateRange) return null;
  
  return {
    start: serializeDate(dateRange.start),
    end: serializeDate(dateRange.end),
  };
};

/**
 * Deserialize date range object
 */
export const deserializeDateRange = (dateRange) => {
  if (!dateRange) return null;
  
  return {
    start: deserializeDate(dateRange.start),
    end: deserializeDate(dateRange.end),
  };
};

/**
 * Clean object from non-serializable data
 */
export const cleanNonSerializableData = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;

  const cleaned = {};

  for (const [key, value] of Object.entries(obj)) {
    if (value === null || value === undefined) {
      cleaned[key] = value;
    } else if (moment.isMoment(value)) {
      cleaned[key] = serializeDate(value);
    } else if (value && typeof value === 'object' && value.isDayjsObject) {
      cleaned[key] = serializeDate(value);
    } else if (value instanceof Date) {
      cleaned[key] = serializeDate(value);
    } else if (typeof value === 'function') {
      // Skip functions
      continue;
    } else if (Array.isArray(value)) {
      cleaned[key] = value.map(item => cleanNonSerializableData(item));
    } else if (typeof value === 'object') {
      cleaned[key] = cleanNonSerializableData(value);
    } else {
      cleaned[key] = value;
    }
  }

  return cleaned;
};

/**
 * Redux middleware to automatically serialize dates
 */
export const dateSerializationMiddleware = (store) => (next) => (action) => {
  if (action.payload && typeof action.payload === 'object') {
    action.payload = cleanNonSerializableData(action.payload);
  }
  
  return next(action);
};

/**
 * Helper to create serializable date state
 */
export const createDateState = (date) => ({
  value: serializeDate(date),
  timestamp: Date.now(),
});

/**
 * Helper to get moment from serializable date state
 */
export const getMomentFromState = (dateState) => {
  if (!dateState || !dateState.value) return null;
  return deserializeDate(dateState.value);
};

/**
 * Validate if object is serializable
 */
export const isSerializable = (obj) => {
  try {
    JSON.stringify(obj);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Get non-serializable paths in object
 */
export const getNonSerializablePaths = (obj, path = '') => {
  const nonSerializablePaths = [];
  
  if (obj === null || obj === undefined) return nonSerializablePaths;
  
  if (typeof obj === 'function') {
    nonSerializablePaths.push(path || 'root');
    return nonSerializablePaths;
  }
  
  if (moment.isMoment(obj) || obj instanceof Date) {
    nonSerializablePaths.push(path || 'root');
    return nonSerializablePaths;
  }
  
  if (typeof obj === 'object') {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;
      nonSerializablePaths.push(...getNonSerializablePaths(value, currentPath));
    }
  }
  
  return nonSerializablePaths;
};

export default {
  serializeDate,
  serializeDateISO,
  deserializeDate,
  serializeDateRange,
  deserializeDateRange,
  cleanNonSerializableData,
  dateSerializationMiddleware,
  createDateState,
  getMomentFromState,
  isSerializable,
  getNonSerializablePaths,
};
