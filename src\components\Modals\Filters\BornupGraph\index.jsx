import { useEffect, useState } from "react";
import { React } from "react";
import {
  Checkbox,
  Select,
  DatePicker,
  Button,
  Form,
  Typography,
  Row,
  Col,
  message,
} from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import { otrsGet } from "../../../../service/apiOtrs";
import { filterByOrderOfInput } from "../../../../utils/filterByOrderOfInput";
import moment from "moment";
import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

function BornupFilter(props) {
  const { Title, Text } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { allContracts, allSquads, handlePopover, filter } = props

  function onFilterFailed() { }

  function clear() {
    form.resetFields();
  }

  const sortAlphaObj = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a.name.toLowerCase() > b.name.toLowerCase()) {
        return 1;
      }
      if (a.name.toLowerCase() < b.name.toLowerCase()) {
        return -1;
      }
      return 0;
    });
    return sortedArray;
  };

  const sortAlpha = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a > b) {
        return -1;
      }
      if (a < b) {
        return 1;
      }
      return 0;
    });
    return sortedArray;
  };

  function onFilterFinish(values) {
    filter(values);
    handlePopover();
    console.log(values)
  }
  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column", width: "300px" }}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <Row justify="space-between" align="middle">
        <Title level={5} style={{ fontWeight: 400, margin: "0px" }}>
          Bornup
        </Title>
        <Button type="text" onClick={() => props.handleVisibleChange()}>
          <CloseCircleOutlined />
        </Button>
      </Row>
      <Form.Item
        name="contract"
        rules={[{ required: true, message: "Prencha este campo." }]}
      >
        <Select
          showSearch
          loading={loading}
          style={{ width: "100%" }}
          placeholder="Contrato"
          disabled={allContracts.length === 0}
          optionFilterProp="children"
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option?.props?.children)
          }
        >
          {sortAlphaObj(allContracts).map((val, index) => (
            <Option key={index} value={val.id}>
              {val.name}
            </Option>
          ))}
        </Select>
      </Form.Item>


      <Form.Item
        name="depatarment"
      >
        <Select
          showSearch
          loading={loading}
          style={{ width: "100%" }}
          placeholder="Departamento"
          disabled={allSquads.length === 0}
          optionFilterProp="children"
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option?.props?.children)
          }
        >
          {sortAlphaObj(allSquads).map((val, index) => (
            <Option key={index} value={index.name}>
              {val.name}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="date">
        <DatePicker
          placeholder="Mês"
          picker="month"
          locale={locale}
          format="MMMM/YYYY"
          disabledDate={(current) => {
            // Limitar seleção entre 2 anos atrás e o mês atual
            const twoYearsAgo = moment().subtract(2, 'years').startOf('month');
            const currentMonth = moment().endOf('month');
            return current && (current < twoYearsAgo || current > currentMonth);
          }}
        />
      </Form.Item>


      <Row justify="space-between">
        <Button type="text" onClick={clear}>
          Limpar
        </Button>
        <Button type="primary" htmlType="submit" className="filter-submit-btn">
          Aplicar
        </Button>
      </Row>
    </Form>
  );
}

export default BornupFilter;
