import axios from "axios";

export const otrsCalculateHoursProvider = () => {
  console.log('🔐 OTRS Provider Debug:', {
    hasTokenNew: !!process.env.REACT_APP_TOKEN_NEW,
    tokenNewPreview: process.env.REACT_APP_TOKEN_NEW?.substring(0, 30) + '...',
    hasOtrsAuth: !!process.env.REACT_APP_OTRS_HOURS_AUTH,
    otrsAuthPreview: process.env.REACT_APP_OTRS_HOURS_AUTH?.substring(0, 30) + '...',
    baseURL: process.env.REACT_APP_API_OTRS_URL,
    usingOriginalApproach: true
  });

  // Tentar primeiro com REACT_APP_OTRS_HOURS_AUTH
  const authToken = process.env.REACT_APP_OTRS_HOURS_AUTH || process.env.REACT_APP_TOKEN_NEW;

  console.log('🔄 Tentando com token OTRS específico:', {
    usingOtrsAuth: !!process.env.REACT_APP_OTRS_HOURS_AUTH,
    tokenPreview: authToken?.substring(0, 30) + '...'
  });

  return axios.create({
    baseURL: process.env.REACT_APP_API_OTRS_URL || "",
    headers: {
      Authorization: authToken,
    },
  });
};

export function updateConsumptionHourContract({ contractID }) {
  const provider = otrsCalculateHoursProvider();

  console.log("Atualizando consumo de horas");

  const payload = { contractID };

  if (contractID) {
    setTimeout(() => {
      provider
        .post("/reports/surplus/all", payload)
        .then(() => {
          console.log("Atualizando consumo de horas");
        })
        .catch(() => {
          console.log(
            "Não foi possivel atualizar o consumo de horas deste contrato"
          );
        });
    }, 3000);
  }
}
