import { checkIfItsmIdAlreadyExists } from "../../../../components/Modals/Contracts/controllers/editContract";

describe("checkIfItsmIdAlreadyExists", () => {
  const mockContracts = [
    {
      id: "contract-1",
      name: "Contrato A",
      identifications: { itsm_id: 123 },
    },
    {
      id: "contract-2", 
      name: "Contrato B",
      identifications: { itsm_id: 456 },
    },
    {
      id: "contract-3",
      name: "Contrato C", 
      identifications: { itsm_id: "789" },
    },
    {
      id: "contract-4",
      name: "Contrato D",
      identifications: { itsm_id: null },
    },
    {
      id: "contract-5",
      name: "Contrato E",
      // sem identifications
    },
  ];

  describe("quando encontra ID duplicado", () => {
    it("deve retornar o contrato duplicado quando itsm_id já existe", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, 123);
      
      expect(result).not.toBeNull();
      expect(result.id).toBe("contract-1");
      expect(result.identifications.itsm_id).toBe(123);
    });

    it("deve retornar o contrato duplicado quando itsm_id é string", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, "456");
      
      expect(result).not.toBeNull();
      expect(result.id).toBe("contract-2");
      expect(result.identifications.itsm_id).toBe(456);
    });

    it("deve retornar o contrato duplicado quando itsm_id é number mas existe como string", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, 789);
      
      expect(result).not.toBeNull();
      expect(result.id).toBe("contract-3");
      expect(result.identifications.itsm_id).toBe("789");
    });
  });

  describe("quando não encontra ID duplicado", () => {
    it("deve retornar null quando itsm_id não existe", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, 999);
      
      expect(result).toBeNull();
    });

    it("deve retornar null quando é o mesmo contrato sendo editado", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, 123, "contract-1");
      
      expect(result).toBeNull();
    });
  });

  describe("validação de parâmetros", () => {
    it("deve retornar null quando contracts é null", () => {
      const result = checkIfItsmIdAlreadyExists(null, 123);
      
      expect(result).toBeNull();
    });

    it("deve retornar null quando contracts é undefined", () => {
      const result = checkIfItsmIdAlreadyExists(undefined, 123);
      
      expect(result).toBeNull();
    });

    it("deve retornar null quando contracts não é array", () => {
      const result = checkIfItsmIdAlreadyExists("not-an-array", 123);
      
      expect(result).toBeNull();
    });

    it("deve retornar null quando itsm_id é null", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, null);
      
      expect(result).toBeNull();
    });

    it("deve retornar null quando itsm_id é undefined", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, undefined);
      
      expect(result).toBeNull();
    });

    it("deve retornar null quando itsm_id é string vazia", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, "");
      
      expect(result).toBeNull();
    });

    it("deve retornar null quando itsm_id é string com apenas espaços", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, "   ");
      
      expect(result).toBeNull();
    });
  });

  describe("casos especiais de contratos", () => {
    it("deve ignorar contratos sem identifications", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, 123);
      
      // Deve encontrar contract-1, não contract-5 que não tem identifications
      expect(result).not.toBeNull();
      expect(result.id).toBe("contract-1");
    });

    it("deve ignorar contratos com itsm_id null", () => {
      const contractsWithNull = [
        {
          id: "contract-null",
          identifications: { itsm_id: null },
        },
        {
          id: "contract-valid",
          identifications: { itsm_id: 123 },
        },
      ];

      const result = checkIfItsmIdAlreadyExists(contractsWithNull, 123);
      
      expect(result).not.toBeNull();
      expect(result.id).toBe("contract-valid");
    });
  });

  describe("comparação de strings", () => {
    it("deve fazer trim nos IDs antes de comparar", () => {
      const contractsWithSpaces = [
        {
          id: "contract-spaces",
          identifications: { itsm_id: " 123 " },
        },
      ];

      const result = checkIfItsmIdAlreadyExists(contractsWithSpaces, "123");
      
      expect(result).not.toBeNull();
      expect(result.id).toBe("contract-spaces");
    });

    it("deve comparar IDs convertidos para string", () => {
      const result = checkIfItsmIdAlreadyExists(mockContracts, "123");
      
      expect(result).not.toBeNull();
      expect(result.id).toBe("contract-1");
      expect(result.identifications.itsm_id).toBe(123); // original é number
    });
  });
});
