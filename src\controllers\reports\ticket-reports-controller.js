import { store } from "../../store/store";
import { allOption } from "../../store/reducers/tickets-report-reduce";
import { getCustomerByParameter } from "../../controllers/clients/clientsController";
import { otrsWebserviceProvider } from "../../provider/otrs-webservice-provider";
import { setTicketReportState } from "../../store/actions/ticket-report-action";
import { Tag, message } from "antd";
import { differenceInDays, differenceInMinutes } from "date-fns";
import moment from "moment";

export async function getCustomerList() {
  const route = "customers/read/hasActiveContracts";
  const customers = await getCustomerByParameter(route, {
    status: 1,
  });

  const list = customers
    .map((c) => {
      let name = "";
      if (c.names.name) name = c.names.name;
      else name = c.names.fantasy_name;

      name = `${c.dsm_id} - ${name}`;

      return { value: name, label: name, itsm_id: c.identifications.itsm_id };
    })
    .sort((a, b) => a.value.localeCompare(b.value));
  return list;
}

export function getCustomerByName(customerName) {
  const { customers } = store.getState().ticketReport;
  return customers.find((c) => customerName === c.value);
}

export function filteredContractsByCustomer(contracts, customerSelected) {
  let list = contracts;
  if (customerSelected && customerSelected.value !== "Todos") {
    list = contracts.filter(
      (c) =>
        c.customer?.itsm_id?.toString() === customerSelected?.itsm_id.toString()
    );
  }

  const contractList = list
    .map((c) => {
      let name = `${c.dsm_id} - ${c.name}`;
      let contractItsmId = c?.identifications?.itsm_id?.toString();
      return { value: contractItsmId, label: name };
    })
    .sort((a, b) => a.label.localeCompare(b.label));

  contractList.unshift(allOption);
  return contractList;
}

export const formatParams = (customerSelected, month) => {
  if (!customerSelected || !month) throw new Error("missing params");
  const customer = customerSelected.itsm_id;

  let date;
  if (typeof month?.format === 'function') {
    date = month.format("MM/YYYY");
  } else if (moment.isMoment(month)) {
    date = month.format("MM/YYYY");
  } else if (month?.isDayjsObject) {
    date = month.format("MM/YYYY");
  } else {
    const momentDate = moment(month);
    if (momentDate.isValid()) {
      date = momentDate.format("MM/YYYY");
    } else {
      throw new Error(`Invalid month format: ${typeof month} - ${month}`);
    }
  }

  return { customer, date };
};

export const searchTicketsByDate = async (
  customerSelected,
  month,
  contract
) => {
  setTicketReportState({ field: "loading", value: true });
  const provider = otrsWebserviceProvider();
  const params = formatParams(customerSelected, month);
  const { data } = await provider.get(
    `/tickets/${params.customer}/${params.date}`
  );

  if (contract !== "Todos")
    setTicketReportState({
      field: "filteredTickets",
      value: data.filter((t) => t.contract_id == contract),
    });

  setTicketReportState({ field: "tickets", value: data });
  setTicketReportState({ field: "loading", value: false });
};

export const getDetailedTicketsReport = async (customerSelected, month) => {
  const provider = otrsWebserviceProvider();
  const params = formatParams(customerSelected, month);
  let results = [];

  try {
    const { data: totalData } = await provider.get(
      `tickets/reports/${params.customer}/${params.date}?page=1`
    );

    results = totalData.result;
    const totalPages = totalData.totalPages || 1;

    if (totalPages >= 3)
      message.warning(
        "Parece que esse cliente tem muitos dados, não se preocupe, estamos cuidando disso!"
      );

    if (totalPages > 1) {
      for (let page = 2; page <= totalPages; page++) {
        let currentRequestResult = await provider.get(
          `tickets/reports/${params.customer}/${params.date}?page=${page}`
        );
        results = results.concat(currentRequestResult.data.result);
      }
    } else {
      results = totalData.result;
    }
    message.success("Dados carregados com sucesso!");
  } catch (error) {
    console.error("Erro ao buscar os dados:", error);
  }

  return results;
};

export const formatTagViewOnTable = (text) => {
  if (!text) throw new Error("text is required");
  switch (text) {
    case "closed successful":
    case "pending auto close+":
      return <Tag color="green">{text}</Tag>;
    case "open":
      return <Tag color="warning">{text}</Tag>;
    case "closed unsuccessful":
      return <Tag color="error">{text}</Tag>;
    default:
      return <Tag>{text}</Tag>;
  }
};

export const handleViewDetailsModalOpen = async (
  ticketNumber,
  setLoading,
  setDataSource,
  setOpen
) => {
  try {
    setLoading(true);
    setOpen(true);
    const provider = otrsWebserviceProvider();
    const { data } = await provider.get(
      `/articles?ticket_number=${ticketNumber}`
    );
    setDataSource(removeSignatureFromArticleBody(data));
    setLoading(false);
    return data;
  } catch (err) {
    console.log(err);
  }
};

export const removeSignatureFromArticleBody = (data) => {
  const signaturePattern = "[1]";
  const articles = data.map((article) => {
    let articleDescription =
      article.article_description || article.article_body;
    try {
      let indexToRemove = articleDescription?.indexOf(signaturePattern);
      if (indexToRemove !== -1) {
        articleDescription = articleDescription.substring(0, indexToRemove);
      } else {
        articleDescription = articleDescription;
      }
      article.article_description = articleDescription;
      return article;
    } catch (err) {
      return article;
    }
  });

  return articles;
};

export const formatTicketAge = (ticket) => {
  let ticketAge = {};

  let currentTicketCreateTime = !ticket.ticket_create_time
    ? new Date(ticket.create_time)
    : new Date(ticket.ticket_create_time);
  let currentDate = new Date();

  if (!ticket) throw new Error("ticket is required");
  switch (ticket.ticket_state) {
    case "closed successful":
      ticketAge = {
        color: "success",
      };
      if (
        differenceInDays(currentDate, currentTicketCreateTime).toFixed(0) > 0 ||
        differenceInDays(currentDate, currentTicketCreateTime) > 0
      ) {
        ticketAge = {
          ...ticketAge,
          age: `${differenceInDays(
            currentDate,
            currentTicketCreateTime
          ).toFixed(0)} d`,
        };
      } else {
        ticketAge = {
          ...ticketAge,
          age: `${differenceInMinutes(
            new Date(),
            currentTicketCreateTime
          ).toFixed(0)} min`,
        };
      }
      break;
    default:
      ticketAge = {
        color: "warning",
      };
      if (
        differenceInDays(currentDate, currentTicketCreateTime).toFixed(0) > 0 ||
        differenceInDays(currentDate, currentTicketCreateTime) > 0
      ) {
        ticketAge = {
          ...ticketAge,
          age: `${differenceInDays(
            currentDate,
            currentTicketCreateTime
          ).toFixed(0)} d`,
        };
      } else {
        ticketAge = {
          ...ticketAge,
          age: `${differenceInMinutes(
            currentDate,
            currentTicketCreateTime
          ).toFixed(0)} min`,
        };
      }
  }

  return ticketAge;
};

export const removeEspecialChars = (text) => {
  if (!text) return text;
  let formattedText = text;
  let removeSafeLinksRegex = /(https?:\/\/[^\s]+)/g;
  let removeUnsafelinksRegex = /http:\/\/[^\s]+/g;
  let removeQuotationMarksRegex = /["]/g;
  formattedText = text.replace(removeSafeLinksRegex, "");
  formattedText = text.replace(removeUnsafelinksRegex, "");
  formattedText = text.replace(removeQuotationMarksRegex, "");
  return removePreviousResponses(formattedText);
};

export const removePreviousResponses = (text) => {
  if (!text) return text;
  let markersRegex = /\[\d+\]/g;
  let lastMarkerPosition = text.search(markersRegex + "$");
  if (lastMarkerPosition !== -1) {
    text = text.substring(0, lastMarkerPosition);
  }
  return text;
};

export const removeBreakRow = (data) => {
  return data.map((ticket) => {
    let article_body = removeEspecialChars(ticket.article_body);
    return {
      ...ticket,
      article_description: article_body.replace(/\n/g, " "),
    };
  });
};
