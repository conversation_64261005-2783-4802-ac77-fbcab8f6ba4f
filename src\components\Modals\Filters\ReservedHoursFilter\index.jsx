import { useState } from "react";
import {
  Select,
  But<PERSON>,
  Form,
  Row,
  Typography,
  DatePicker,
  message,
} from "antd";
import { filterByOrderOfInput } from "../../../../utils/filterByOrderOfInput";
import { differenceInCalendarDays } from "date-fns";
import moment from "moment";
import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

function ReservedHoursFilter({
  allClients,
  allWallets,
  getFilteredContracts,
  getFilteredContractByClient,
  getGraphData,
  hoursConsumed,
  handlePopoverVisibility,
  setFilterSelected,
  consumedHours,
}) {
  const { Title, Text } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();
  const [dateErrorStatus, setDateErrorStatus] = useState(false);
  const [filter, setFilter] = useState(false);

  function onFilterFinish(values) {
    if (dateErrorStatus) {
      message.error("Período inválido, apenas até 5 dias");
      return false;
    } else {
      setFilterSelected(true);
      getGraphData(values);
      handlePopoverVisibility();
    }
  }

  let formatMoment = {
    startDate: "",
    endDate: "",
  };

  const validateDate = (e) => {
    setDateErrorStatus(false);
    let start;
    let end;
    if (e[0] && e[1]) {
      start = moment(e[0]?._d).format("YYYY-MM-DD") + "T00:00:00:000Z";
      end = moment(e[1]?._d).format("YYYY-MM-DD") + "T00:00:00:000Z";
      formatMoment.startDate = moment(e[0]?._d).format("YYYY-MM-DD");
      formatMoment.endDate = moment(e[1]?._d).format("YYYY-MM-DD");
    }

    if (differenceInCalendarDays(e[1]?._d, e[0]?._d) >= 5) {
      setDateErrorStatus(true);
      formatMoment.startDate = "";
      formatMoment.endDate = "";
    }
  };

  function clear() {
    form.resetFields();
  }

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column", width: "450px" }}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      autoComplete="off"
    >
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <Title level={5} style={{ fontWeight: 400 }}>
          {consumedHours ? "Acima/abaixo do contratado" : "Horas reservadas"}
        </Title>
        <Button type="text" onClick={() => handlePopoverVisibility()}>
          X
        </Button>
      </div>
      <Form.Item name="wallet">
        <Select
          showSearch
          className="w-full"
          placeholder="Selecionar carteira"
          optionFilterProp="children"
          onChange={(value) => getFilteredContracts(value)}
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option.props.children)
          }
        >
          {allWallets.map((wallet) => {
            return (
              <Option key={wallet.id} value={wallet.name}>
                {wallet.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Form.Item
        name="client"
        rules={[
          {
            required: true,
            message: "Por favor, preencha o campo",
          },
        ]}
      >
        <Select
          showSearch
          className="w-full"
          placeholder="Selecionar cliente"
          optionFilterProp="children"
          onChange={(value) => {
            setFilter(true);
            getFilteredContractByClient(value);
          }}
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option.props.children)
          }
          filterSort={(optionA, optionB) => {
            return optionA.children
              .toLowerCase()
              .localeCompare(optionB.children.toLowerCase());
          }}
        >
          {allClients.map((client, index) => {
            return (
              <Option key={index} value={client?.identifications?.itsm_id}>
                {client?.names?.name || client?.names?.fantasy_name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>
      <Form.Item
        name="rangeDate"
        rules={[
          {
            required: true,
            message: "Por favor, preencha o campo",
          },
        ]}
      >
        {hoursConsumed === true ? (
          <DatePicker
            placeholder="Selecione um mês"
            picker="month"
            style={{ width: "100%" }}
            locale={locale}
            format="MMMM/YYYY"
            disabledDate={(current) => {
              // Limitar seleção entre 2 anos atrás e o mês atual
              const twoYearsAgo = moment().subtract(2, 'years').startOf('month');
              const currentMonth = moment().endOf('month');
              return current && (current < twoYearsAgo || current > currentMonth);
            }}
          />
        ) : (
          <DatePicker.RangePicker
            style={{ width: "100%" }}
            locale={locale}
            format="DD/MM/YYYY"
            status={dateErrorStatus === true ? "error" : ""}
            placeholder={
              dateErrorStatus === true
                ? ["Período inválido", "Período inválido"]
                : ["Data inicial", "Data final"]
            }
            value={
              dateErrorStatus === true
                ? ["", ""]
                : [formatMoment.startMoment, formatMoment.endMoment]
            }
            disabledDate={(current) => {
              // Limitar seleção entre 2 anos atrás e a data atual
              const twoYearsAgo = moment().subtract(2, 'years').startOf('day');
              const today = moment().endOf('day');
              return current && (current < twoYearsAgo || current > today);
            }}
            onChange={(e) => validateDate(e)}
          />
        )}
      </Form.Item>
      {dateErrorStatus && (
        <Text type="danger">Escolha um período dentro de 5 dias*</Text>
      )}
      <Form.Item name="buttons">
        <Row justify="space-between">
          <Button type="default" onClick={clear}>
            {" "}
            Limpar{" "}
          </Button>
          <Button htmlType="submit" type="primary">
            {" "}
            Aplicar{" "}
          </Button>
        </Row>
      </Form.Item>
    </Form>
  );
}

export default ReservedHoursFilter;
