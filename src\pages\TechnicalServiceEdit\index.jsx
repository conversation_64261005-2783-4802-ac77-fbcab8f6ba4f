import React, { useEffect, useState } from "react";
import { CheckOutlined, InfoCircleOutlined } from "@ant-design/icons";
import {
  Layout,
  Form,
  Card,
  Row,
  Col,
  Space,
  Input,
  Button,
  Typography,
  Affix,
  message,
  Tooltip,
  Popconfirm,
  Select,
} from "antd";
import {
  DeleteOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined,
  ArrowUpOutlined,
} from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { useLocation, useNavigate } from "react-router-dom";
import { v4 } from "uuid";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { TextColor } from "../../hooks/ColorInverter";

export const TechnicalServiceEdit = () => {
  const navigate = useNavigate();
  const { Content } = Layout;
  const { TextArea } = Input;
  const { Title, Text } = Typography;
  const { Option } = Select;
  const { state } = useLocation();
  const [service_form] = Form.useForm();
  const [activities_form] = Form.useForm();
  const [allServices, setAllServices] = useState([]);
  const [existing, setExisting] = useState(false);
  const [toTopVisible, setToTopVisible] = useState(false);
  const [tagName, setTagName] = useState(state[0]?.tag.name);
  const [serviceName, setServiceName] = useState(state[0]?.name);
  const [serviceDescription, setServiceDescription] = useState(
    state[0]?.description
  );
  const [disabled, setDisabled] = useState(true);
  const [mainTagColorInput, setMainTagColorInput] = useState("#0f9347");
  const [collapsed, setCollapsed] = useState(false);
  const [activities, setActivities] = useState([]);
  const [proposalData, setProposalData] = useState([]);
  const [sumHoursForType, setSumHoursForType] = useState([
    { SETUP247Sum: 0 },
    { SETUP85Sum: 0 },
    { SUST247Sum: 0 },
    { SUST85Sum: 0 },
    { DBA247Sum: 0 },
    { DBA85Sum: 0 },
  ]);
  const [loading, setLoading] = useState(false);
  const [selectTagToggle, setSelectTagToggle] = useState(false);

  function uniqByKeepLast(data, key) {
    return [...new Map(data.map((x) => [key(x), x]).values())];
  }
  let arr = [];

  const newArr = uniqByKeepLast(state[1], (e) => e.tag.name);

  newArr.forEach((e) => {
    return arr.push(e[1]);
  });

  const handleAddSubActivity = (activityKey) => {
    setActivities((prevState) => prevState.map((item) => {
        if (item.id === activityKey) {
          return {
            ...item,
            subtasks: [...item.subtasks, { index: v4(), value: "" }],
          };
        }
        return item;
      }));
  };

  const handleRemoveSubActivity = (activityKey, id, checkOrigin) => {
    setActivities((prevState) => prevState.map((item) => {
        if (item.id === activityKey) {
          return {
            ...item,
            subtasks: item.subtasks.filter((item) => item.index !== id),
          };
        }
        return item;
      }));
    if (checkOrigin === true) {
      message.warning("Sub Atividades vazias removidas!");
    } else {
      message.warning("Sub Atividade removida com sucesso!");
    }
  };

  const handleSubActivityChange = (value, id, activityKey) => {
    setActivities((prevState) =>
      prevState.map((item) => {
        if (item.id === activityKey) {
          return {
            ...item,
            subtasks: item.subtasks.map((item) => {
              if (item.index === id) {
                return {
                  ...item,
                  value: value,
                };
              }

              return item;
            }),
          };
        }

        return item;
      })
    );
  };

  const handleAddActivity = (prevIndex) => {
    setActivities((prev) => [
      ...prev,
      {
        id: prevIndex + 1,
        subtasks: [{ index: v4(), value: "" }],
        name: "",
        estimates: {
          "8x5setup": 0,
          "24x7setup": 0,
          "8x5sust": 0,
          "24x7sust": 0,
          "8x5dbasec": 0,
          "24x7dbasec": 0,
        },
      },
    ]);
  };

  const handleRemoveActivity = (index) => {
    setActivities((prev) => prev.filter(({ id }) => index !== id));
    message.warning("Atividade removida com sucesso!");
  };

  const handleActivityChange = (value, id, field) => {
    setActivities((prev) => prev.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            estimates: {
              ...item.estimates,
              [field]: value,
            },
            [field]: value,
          };
        }
        return item;
      }));
  };

  const handleNullSubtask = () => {
    activities.map((item) => {
      return item.subtasks.map((subItem, subItemKey) => {
        if (subItem.value === "" && item.subtasks.length > 1) {
          handleRemoveSubActivity(item.id, subItem.index, true);
        }
      });
    });
  };

  const handleCheckServiceName = (inputValue) => {
    if (
      allServices.find(
        (service) => service.name === inputValue && inputValue !== state[0].name
      ) !== undefined
    ) {
      setExisting(true);
      message.warning(
        `Nome de serviço '${inputValue}' já existe! Por favor insira outro nome.`
      );
    } else {
      setExisting(false);
    }
  };

  const handleCheckServiceDescription = (inputValue) => {
    if (
      allServices.find(
        (service) =>
          service.description === inputValue &&
          inputValue !== state[0].description
      ) !== undefined
    ) {
      setExisting(true);
      message.warning(
        `Descrição de serviço '${inputValue}' já existe! Por favor insira outra descrição.`
      );
    } else {
      setExisting(false);
    }
  };

  const handleSubmit = async (data) => {
    setLoading(true);
    data["tag_name"] = tagName;
    data["tag_color"] = mainTagColorInput;
    handleNullSubtask();
    if (existing) {
      message.error("Já existe um serviço com essas propriedades!");
    } else {
      localStorage.setItem(
        `proposal-service-${state[0].id}`,
        JSON.stringify({
          id: state[0].id,
          name: serviceName,
          description: serviceDescription,
          active: true,
          management: false,
          tag: {
            name: tagName,
            rgb: data.tag_color,
          },
          tasks: activities.map((item) => ({
            name: item.name,
            description: item.description,
            estimates: {
              "8x5dbasec": Number(item.estimates["8x5dbasec"]),
              "8x5setup": Number(item.estimates["8x5setup"]),
              "8x5sust": Number(item.estimates["8x5sust"]),
              "24x7dbasec": Number(item.estimates["24x7dbasec"]),
              "24x7setup": Number(item.estimates["24x7setup"]),
              "24x7sust": Number(item.estimates["24x7sust"]),
            },
            subtasks: item.subtasks
              .map((currentSubtask, id) => {
                if (id === 0) {
                  if (currentSubtask.value !== "") {
                    return { description: currentSubtask.value };
                  } else if (
                    item?.subtasks?.length > 1 &&
                    currentSubtask.value === ""
                  ) {
                    return { description: "" };
                  } else {
                    return {
                      description: "Atividade sem Sub Atividades definidas",
                    };
                  }
                } else {
                  return { description: currentSubtask.value };
                }
              })
              .filter((item) => item.description !== "")
              .map((item) =>
                item.description === "Atividade sem Sub Atividades definidas"
                  ? { description: "" }
                  : { description: item.description }
              ),
          })),
        })
      );

      if (state[0].id) {
        const username = localStorage.getItem("@dsm/username");
        const title = "Edição de Serviço";
        const description = `${username} editou o serviço: ${serviceName}`;
        logNewAuditAction(username, title, description);
        message.success("Serviço atualizado com sucesso!");
        navigate(-1);
      }
    }
    setLoading(false);
  };

  const estimatedActivities = [
    {
      title: "24x7 SETUP",
      placeholder: "0 hrs",
      form_title: "24x7setup",
    },
    {
      title: "8x5 SETUP",
      placeholder: "0 hrs",
      form_title: "8x5setup",
    },
    {
      title: "24x7 SUST",
      placeholder: "0 hrs",
      form_title: "24x7sust",
    },
    {
      title: "8x5 SUST",
      placeholder: "0 hrs",
      form_title: "8x5sust",
    },
    {
      title: "24x7 DBA / SEC",
      placeholder: "0 hrs",
      form_title: "24x7dbasec",
    },
    {
      title: "8x5 DBA / SEC",
      placeholder: "0 hrs",
      form_title: "8x5dbasec",
    },
  ];

  const screenScroll = () => {
    document.documentElement.scrollTop > 360
      ? setToTopVisible(true)
      : setToTopVisible(false);
  };

  useEffect(() => {
    async function getServices() {
      setAllServices(
        await dynamoGet(`${process.env.REACT_APP_STAGE}-services`)
      );
    }

    getServices();
  }, []);

  useEffect(() => {
    window.addEventListener("scroll", screenScroll);
  }, []);

  useEffect(() => {
    setSumHoursForType([
      {
        SETUP247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7setup"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7setup"]);
            return sum;
          }
          return acc;
        }, 0),
        SETUP85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5setup"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5setup"]);
            return sum;
          }
          return acc;
        }, 0),
        SUST247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7sust"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7sust"]);
            return sum;
          }
          return acc;
        }, 0),
        SUST85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5sust"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5sust"]);
            return sum;
          }
          return acc;
        }, 0),
        DBA247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7dbasec"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7dbasec"]);
            return sum;
          }
          return acc;
        }, 0),
        DBA85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5dbasec"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5dbasec"]);
            return sum;
          }
          return acc;
        }, 0),
      },
    ]);
    activities.map((item) => {
      if (
        Object.values(item.estimates).find(
          (value) => value !== "" && value > 0
        ) &&
        item.name &&
        item.description &&
        tagName !== "" &&
        serviceName &&
        serviceName !== "" &&
        serviceDescription &&
        serviceDescription !== ""
      ) {
        setDisabled(false);
      } else {
        setDisabled(true);
      }
    });
  }, [activities, serviceName, serviceDescription, tagName]);

  useEffect(() => {
    service_form.setFieldsValue({
      name: state[0].name,
      tag_name: tagName,
      tag_color: state[0].tag.rgb,
      description: state[0].description,
    });

    state[0].tasks.map((item, indexKey) => {
      return setActivities((prev) => [
        ...prev,

        {
          id: indexKey,
          name: item.name,
          description: item.description,
          estimates: {
            "8x5dbasec": item.estimates["8x5dbasec"],
            "8x5setup": item.estimates["8x5setup"],
            "8x5sust": item.estimates["8x5sust"],
            "24x7dbasec": item.estimates["24x7dbasec"],
            "24x7setup": item.estimates["24x7setup"],
            "24x7sust": item.estimates["24x7sust"],
          },
          subtasks: item.subtasks.map((item, id) => {
            return {
              index: id,
              value: item.description,
            };
          }),
        },
      ]);
    });
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Popconfirm
              title="Ao voltar, você perderá as alterações, tem certeza?"
              onConfirm={() => navigate(-1)}
              okText="Sim"
              cancelText="Não"
            >
              <Button type="text" icon={<ArrowLeftOutlined />}>
                <Space>
                  <Title
                    level={5}
                    style={{ margin: "0 15px", fontWeight: 400 }}
                  >
                    Editar Serviço
                  </Title>
                </Space>
              </Button>
            </Popconfirm>
            <Row justify="center">
              <Col span={24}>
                <Form
                  form={service_form}
                  layout="vertical"
                  requiredMark={false}
                  onFinish={handleSubmit}
                >
                  <Row gutter={[20, 12]} justify="space-between">
                    <Col lg={16} sm={24}>
                      <Row justify="space-between">
                        <Col span={12}>
                          <Row>
                            <Text style={{ marginBottom: ".5rem" }}>
                              Nome{" "}
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontWeight: "bold",
                                }}
                              >
                                *
                              </span>
                            </Text>
                          </Row>
                          <Row>
                            <Form.Item name="name">
                              <Input
                                placeholder="Nome do serviço"
                                value={serviceName}
                                style={{
                                  borderColor: !serviceName ? "#ff4d4f" : "",
                                  marginTop: 8,
                                }}
                                onChange={(e) => setServiceName(e.target.value)}
                              />
                              {!serviceName && (
                                <span
                                  style={{
                                    color: "#ff4d4f",
                                    fontSize: 13,
                                  }}
                                >
                                  * valor não informado, campo obrigatório.
                                </span>
                              )}
                            </Form.Item>
                          </Row>
                        </Col>
                        <Col span={11}>
                          <Tooltip
                            placement="top"
                            title={
                              <>
                                {selectTagToggle === false
                                  ? "Deseja utilizar uma tag existente?"
                                  : "Deseja criar uma nova tag"}
                                <Row justify="end">
                                  <Col>
                                    <Button
                                      type="primary"
                                      icon={<CheckOutlined />}
                                      onClick={() =>
                                        setSelectTagToggle(!selectTagToggle)
                                      }
                                    ></Button>
                                  </Col>
                                </Row>
                              </>
                            }
                          >
                            {selectTagToggle == false ? (
                              <>
                                <Row>
                                  <Text style={{ marginBottom: ".5rem" }}>
                                    Nome da Tag{" "}
                                    <span
                                      style={{
                                        color: "#ff4d4f",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      *
                                    </span>
                                  </Text>
                                </Row>
                                <Row>
                                  <Col span={22}>
                                    <Form.Item name="tag_name">
                                      <Input
                                        placeholder="Nome da tag"
                                        value={tagName}
                                        style={{
                                          borderColor: !tagName
                                            ? "#ff4d4f"
                                            : "",
                                          marginTop: 8,
                                        }}
                                        onChange={(event) => {
                                          setTagName(event.target.value);
                                        }}
                                      />
                                    </Form.Item>
                                  </Col>
                                  <Col span={1}>
                                    <Row>
                                      <Form.Item name="tag_color">
                                        <Input
                                          type="color"
                                          value={mainTagColorInput}
                                          onChange={(event) => {
                                            setMainTagColorInput(
                                              event.target.value
                                            );
                                          }}
                                          style={{
                                            width: "2rem",
                                            padding: "0",
                                            clipPath: "circle(32% at center)",
                                            border: "none",
                                            outline: "none",
                                            boxShadow: "none",
                                            marginTop: 8,
                                          }}
                                        />
                                      </Form.Item>
                                    </Row>
                                  </Col>
                                </Row>
                              </>
                            ) : (
                              <>
                                <Row>
                                  <Text style={{ marginBottom: ".5rem" }}>
                                    Nome da Tag{" "}
                                    <span
                                      style={{
                                        color: "#ff4d4f",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      *
                                    </span>
                                  </Text>
                                </Row>
                                <Row>
                                  <Col span={24}>
                                    <Form.Item name="tag_name">
                                      <Select
                                        placeholder="Nome da tag"
                                        style={{
                                          marginTop: 8,
                                        }}
                                        onChange={(e, i) => {
                                          setTagName(e);
                                          state[1]?.map((i) =>
                                            i.tag.name === e
                                              ? setMainTagColorInput(i.tag.rgb)
                                              : null
                                          );
                                        }}
                                      >
                                        {arr.map((item) => {
                                          return (
                                            <Option value={item.tag.name}>
                                              {TextColor(
                                                item?.tag.rgb,
                                                item?.tag.name
                                              )}
                                            </Option>
                                          );
                                        })}
                                      </Select>
                                    </Form.Item>
                                  </Col>
                                </Row>
                              </>
                            )}
                          </Tooltip>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={24}>
                          <Form.Item name="description">
                            <Text>
                              Descrição de serviço{" "}
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontWeight: "bold",
                                }}
                              >
                                *
                              </span>
                            </Text>
                            <TextArea
                              rows={6}
                              placeholder="Insira a descrição do serviço"
                              value={serviceDescription}
                              onChange={(e) =>
                                setServiceDescription(e.target.value)
                              }
                              style={{
                                marginTop: "8px",
                                height: "auto",
                                minHeight: "auto",
                              }}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                    <Col lg={8} sm={24}>
                      <div
                        style={{
                          widht: "100%",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          flexDirection: "column",
                        }}
                      >
                        <Text>Estimativas</Text>
                        <Row
                          gutter={[16, 16]}
                          style={{
                            backgroundColor: "#D9D9D9",
                            padding: "30px",
                            borderRadius: "5px",
                          }}
                        >
                          {estimatedActivities.map(
                            (
                              mainEstimatedActivity,
                              mainEstimatedActivityKey
                            ) => {
                              return (
                                <Col span={12} key={mainEstimatedActivityKey}>
                                  <Row align="center">
                                    {mainEstimatedActivity.title}
                                  </Row>
                                  {sumHoursForType.map(
                                    (
                                      sumHoursForTypeItem,
                                      sumHoursForTypeKey
                                    ) => {
                                      return Object.values(
                                        sumHoursForTypeItem
                                      ).map(
                                        (
                                          sumHoursForTypeItemValue,
                                          sumHoursForTypeValueKey
                                        ) => {
                                          return (
                                            sumHoursForTypeValueKey ===
                                              mainEstimatedActivityKey && (
                                              <Input
                                                placeholder={
                                                  sumHoursForTypeItemValue
                                                }
                                                disabled
                                              />
                                            )
                                          );
                                        }
                                      );
                                    }
                                  )}
                                </Col>
                              );
                            }
                          )}
                        </Row>
                      </div>
                    </Col>
                  </Row>
                </Form>
                <Form
                  layout="vertical"
                  requiredMark={false}
                  form={activities_form}
                  style={{ marginTop: "1rem" }}
                >
                  <Text>Atividades</Text>
                  {activities.map((container, containerKey) => (
                    <Card
                      key={containerKey}
                      style={{
                        backgroundColor: "#f4f4f4",
                        margin: "25px 0",
                        boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
                        borderRadius: "5px",
                      }}
                    >
                      <Row justify="space-between" gutter={[16, 16]}>
                        <Col lg={14} sm={24}>
                          <Row>
                            <Col span={24}>
                              <Form.Item>
                                <Text>
                                  Nome{" "}
                                  <span
                                    style={{
                                      color: "#ff4d4f",
                                      fontWeight: "bold",
                                    }}
                                  >
                                    *
                                  </span>
                                </Text>
                                <Input
                                  placeholder="Nome"
                                  value={activities[containerKey].name}
                                  style={{
                                    borderColor: !activities[containerKey].name
                                      ? "#ff4d4f"
                                      : "",
                                  }}
                                  onChange={(e) =>
                                    handleActivityChange(
                                      e.target.value,
                                      container.id,
                                      "name"
                                    )
                                  }
                                />
                                {!activities[containerKey].name && (
                                  <span
                                    style={{
                                      color: "#ff4d4f",
                                      fontSize: 13,
                                    }}
                                  >
                                    * valor não informado, campo obrigatório.
                                  </span>
                                )}
                              </Form.Item>
                            </Col>
                          </Row>
                          <Row>
                            <Col span={24}>
                              <Form.Item>
                                <Text>
                                  Descrição{" "}
                                  <span
                                    style={{
                                      color: "#ff4d4f",
                                      fontWeight: "bold",
                                    }}
                                  >
                                    *
                                  </span>
                                </Text>
                                <TextArea
                                  rows={6}
                                  value={activities[containerKey].description}
                                  style={{
                                    borderColor: !activities[containerKey]
                                      .description
                                      ? "#ff4d4f"
                                      : "",
                                    height: "auto",
                                    minHeight: "auto",
                                  }}
                                  onChange={(e) =>
                                    handleActivityChange(
                                      e.target.value,
                                      container.id,
                                      "description"
                                    )
                                  }
                                  placeholder="Digite a descrição da atividade"
                                />
                                {!activities[containerKey].description && (
                                  <span
                                    style={{
                                      color: "#ff4d4f",
                                      fontSize: 13,
                                    }}
                                  >
                                    * valor não informado, campo obrigatório.
                                  </span>
                                )}
                              </Form.Item>
                            </Col>
                          </Row>

                          <Row>
                            <Col span={24}>
                              <Text>Sub Atividades</Text>
                              <Form.Item>
                                {container.subtasks.map((item, index) => (
                                  <Row justify="space-between">
                                    <Col
                                      span={20}
                                      style={{ marginTop: ".5rem" }}
                                    >
                                      <Input
                                        rows={1}
                                        disabled={
                                          !activities[containerKey]
                                            .description ||
                                          !activities[containerKey].name
                                        }
                                        placeholder="Descrição"
                                        key={index}
                                        value={item.value}
                                        onChange={(e) =>
                                          handleSubActivityChange(
                                            e.target.value,
                                            item.index,
                                            containerKey
                                          )
                                        }
                                      />
                                    </Col>
                                    <Col span={4}>
                                      <Row>
                                        <Col
                                          span={12}
                                          style={{ marginTop: ".5rem" }}
                                        >
                                          <Button
                                            type="text"
                                            disabled={!item.value}
                                            onClick={() => {
                                              handleAddSubActivity(
                                                containerKey
                                              );
                                            }}
                                          >
                                            <PlusOutlined />
                                          </Button>
                                        </Col>
                                        <Col
                                          span={12}
                                          style={{ marginTop: ".5rem" }}
                                        >
                                          {container.subtasks.length > 1 && (
                                            <Button
                                              danger
                                              type="text"
                                              onClick={() => {
                                                handleRemoveSubActivity(
                                                  containerKey,
                                                  item.index,
                                                  false
                                                );
                                              }}
                                            >
                                              <DeleteOutlined />
                                            </Button>
                                          )}
                                        </Col>
                                      </Row>
                                    </Col>
                                  </Row>
                                ))}
                              </Form.Item>
                            </Col>
                          </Row>
                        </Col>

                        <Col offset={1} lg={9} sm={24}>
                          <Row justify="center" gutter={[16, 16]}>
                            {estimatedActivities.map(
                              (estimatedActivity, estimatedActivityKey) => {
                                return (
                                  <Col span={12}>
                                    <Form.Item>
                                      <Row justify="center">
                                        {estimatedActivity.title}
                                      </Row>
                                      <Input
                                        type="number"
                                        min="0"
                                        placeholder={"0 hrs"}
                                        value={
                                          activities[containerKey].estimates[
                                            estimatedActivity.title
                                              .replace("/", "")
                                              .split(" ")
                                              .join("")
                                              .toLocaleLowerCase()
                                          ]
                                            ? activities[containerKey]
                                                .estimates[
                                                estimatedActivity.title
                                                  .replace("/", "")
                                                  .split(" ")
                                                  .join("")
                                                  .toLocaleLowerCase()
                                              ]
                                            : ""
                                        }
                                        onChange={(e) =>
                                          handleActivityChange(
                                            e.target.value,
                                            container.id,
                                            estimatedActivity.form_title
                                          )
                                        }
                                      ></Input>
                                    </Form.Item>
                                  </Col>
                                );
                              }
                            )}
                          </Row>
                        </Col>
                      </Row>

                      <Row style={{ width: "100%" }} justify="space-between">
                        <Button
                          type="link"
                          style={{ margin: "15px 0" }}
                          onClick={() => {
                            handleAddActivity(container.id);
                          }}
                        >
                          Adicionar Atividade
                          <PlusOutlined />
                        </Button>
                        {activities.length > 1 && (
                          <Button
                            danger
                            type="text"
                            style={{ margin: "15px 0" }}
                            onClick={() => {
                              handleRemoveActivity(container.id);
                            }}
                          >
                            Remover Atividade
                            <DeleteOutlined />
                          </Button>
                        )}
                      </Row>
                    </Card>
                  ))}
                </Form>

                <Row justify="space-between">
                  <Button danger type="primary" onClick={() => navigate(-1)}>
                    Cancelar <CloseCircleOutlined />
                  </Button>
                  {disabled === true ? (
                    <Tooltip
                      placement="top"
                      title={"Verifique os dados das atividades"}
                    >
                      <Button
                        type="primary"
                        onClick={() => service_form.submit()}
                        disabled={disabled}
                        loading={loading}
                      >
                        Concluir
                        <CheckCircleOutlined />
                      </Button>
                    </Tooltip>
                  ) : (
                    <Button
                      type="primary"
                      onClick={() => service_form.submit()}
                      disabled={disabled || !serviceName || !serviceDescription}
                    >
                      Concluir <CheckCircleOutlined />
                    </Button>
                  )}
                </Row>
              </Col>
            </Row>
          </Card>
        </Content>
        {toTopVisible === true && (
          <Affix
            style={{
              clipPath: "circle()",
              position: "fixed",
              right: 5,
              bottom: 5,
              width: "50px",
            }}
          >
            <Button
              style={{ width: "100%" }}
              type="primary"
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            >
              <ArrowUpOutlined />
            </Button>
          </Affix>
        )}
      </Layout>
    </Layout>
  );
};
