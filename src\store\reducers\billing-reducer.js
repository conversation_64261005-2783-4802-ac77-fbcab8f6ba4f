import { createSlice } from "@reduxjs/toolkit";
import { totalValuesInitialState } from "../../constants/totalValuesInitialState";
import dayjs from "dayjs";

const REDUCER_NAME = "billing";

const INITIAL_STATE = {
  loading: false,
  customers: [],
  contracts: [],
  finops: [],
  complianceItems: [],
  complianceMonitoring: [],
  complianceMonitoringLoading: [],
};

const billingSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setAllBillingReduce: (state, action) => {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }
      state[action.payload.field] = value;
    },
    cleanBillingReduce: (state) => {
      Object.keys(INITIAL_STATE).forEach((key) => {
        state[key] = INITIAL_STATE[key];
      });
    },
  },
});

export const { setAllBillingReduce, cleanBillingReduce } = billingSlice.actions;

export default billingSlice.reducer;
