import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";
import { useEffect, useState } from "react";
import { DatePicker, Typography, Col, Space } from "antd";
import { setContractFieldState } from "../../../store/actions/contract-action";

export const DateFilters = () => {
  const [date, setDate] = useState({ dtStart: null, dtEnd: null });
  const { RangePicker } = DatePicker;
  const { Text } = Typography;

  useEffect(() => {
    // Converter objetos moment/dayjs para strings antes de salvar no Redux
    const startValue = date.dtStart ? date.dtStart.format('YYYY-MM-DD') : null;
    const endValue = date.dtEnd ? date.dtEnd.format('YYYY-MM-DD') : null;
    setContractFieldState({ field: "dtStart", value: startValue });
    setContractFieldState({ field: "dtEnd", value: endValue });
  }, [date]);

  const handleOnChange = (moment) => {
    if (moment !== null) {
      setDate({ dtStart: moment[0], dtEnd: moment[1] });
    } else {
      setDate({ dtStart: null, dtEnd: null });
    }
  };

  return (
    <Col span={7}>
      <Text>Filtrar por período:</Text>
      <RangePicker
        picker="month"
        placeholder={["Inicio", "Fim"]}
        value={[date.dtStart, date.dtEnd]} // Usar value controlado ao invés de defaultValue
        locale={locale}
        style={{
          height: "32px",
          width: "100%",
        }}
        onChange={(moment) => {
          handleOnChange(moment);
        }}
      />
    </Col>
  );
};
