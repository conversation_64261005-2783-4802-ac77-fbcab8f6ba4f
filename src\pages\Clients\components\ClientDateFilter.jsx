import { DatePicker, Typography } from "antd";
import { setDateState } from "../../../store/actions/customers-action";
import dayjs from "dayjs";
import "dayjs/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";
import { useEffect, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import {
  clearDateFilterCache,
  hasCorruptedDateCache,
  generateCacheBustingKey,
  forceUpdateDateComponents
} from "../../../utils/dateFilterCache";

export const DateFilters = () => {
  const { RangePicker } = DatePicker;

  const { dtStart, dtEnd } = useSelector(state => state.customers);
  const [key, setKey] = useState(Date.now());
  const [currentValues, setCurrentValues] = useState([null, null]);

  const clearCacheAndUpdate = useCallback(() => {
    clearDateFilterCache();
    setKey(generateCacheBustingKey());
    forceUpdateDateComponents();
  }, []);

  useEffect(() => {
    const newValues = [];

    if (dtStart) {
      try {
        newValues[0] = typeof dtStart === 'string' ? dayjs(dtStart) : dtStart;
      } catch (error) {
        console.warn('Erro ao processar dtStart:', error);
        newValues[0] = null;
      }
    } else {
      newValues[0] = null;
    }

    if (dtEnd) {
      try {
        newValues[1] = typeof dtEnd === 'string' ? dayjs(dtEnd) : dtEnd;
      } catch (error) {
        console.warn('Erro ao processar dtEnd:', error);
        newValues[1] = null;
      }
    } else {
      newValues[1] = null;
    }

    setCurrentValues(newValues);
  }, [dtStart, dtEnd]);

  useEffect(() => {
    if (hasCorruptedDateCache()) {
      clearCacheAndUpdate();
    }

    const handleCacheCleared = (event) => {
      setKey(generateCacheBustingKey());
    };

    window.addEventListener('dateFilterCacheCleared', handleCacheCleared);

    return () => {
      window.removeEventListener('dateFilterCacheCleared', handleCacheCleared);
    };
  }, [clearCacheAndUpdate]);

  // Função para desabilitar datas fora do período permitido
  const disabledDate = (current) => {
    if (!current) return false;

    const now = dayjs();

    if (current.isAfter(now, 'month')) {
      // if (process.env.NODE_ENV === 'development') {
      //   console.log(`Bloqueando mês futuro: ${current.format('YYYY-MM')}`);
      // }
      return true;
    }

    return false;
  };

  return (
    <>
      <Typography.Text>
        Filtrar por período:
        {/* Botão para limpar cache manualmente (apenas em desenvolvimento) */}
        {process.env.NODE_ENV === 'development' && (
          <span
            style={{
              marginLeft: '8px',
              color: '#1890ff',
              cursor: 'pointer',
              fontSize: '12px',
              textDecoration: 'underline'
            }}
            onClick={clearCacheAndUpdate}
            title="Limpar cache do filtro"
          >
            [Limpar Cache]
          </span>
        )}
      </Typography.Text>
      <RangePicker
        key={key} 
        picker="month"
        placeholder={["Inicio", "Fim"]}
        allowClear={true}
        locale={locale}
        value={currentValues[0] && currentValues[1] ? currentValues : undefined}
        style={{ borderRadius: "4px", height: "34px", width: "100%" }}
        disabledDate={disabledDate}
        onChange={(dates) => {
          try {
            if (dates && dates.length === 2) {
              const startDate = dates[0] ? dates[0].format('YYYY-MM-DD') : null;
              const endDate = dates[1] ? dates[1].format('YYYY-MM-DD') : null;

              setDateState({ field: "dtStart", value: startDate });
              setDateState({ field: "dtEnd", value: endDate });

              setCurrentValues([dates[0], dates[1]]);
            } else {
              setDateState({ field: "dtStart", value: null });
              setDateState({ field: "dtEnd", value: null });
              setCurrentValues([null, null]);
            }
          } catch (error) {
            clearCacheAndUpdate();
          }
        }}
        onOpenChange={(open) => {
          if (open) {
            try {
              const reduxStart = dtStart ? (typeof dtStart === 'string' ? dayjs(dtStart) : dtStart) : null;
              const reduxEnd = dtEnd ? (typeof dtEnd === 'string' ? dayjs(dtEnd) : dtEnd) : null;

              if (
                (reduxStart && !currentValues[0]) ||
                (reduxEnd && !currentValues[1]) ||
                (!reduxStart && currentValues[0]) ||
                (!reduxEnd && currentValues[1])
              ) {
                console.warn('DateFilter: Detectada dessincronização, limpando cache');
                clearCacheAndUpdate();
              }
            } catch (error) {
              console.error('Erro ao verificar sincronização:', error);
              clearCacheAndUpdate();
            }
          }
        }}
      />
    </>
  );
};
