import { format, isValid } from "date-fns";

export const filterSignatureDateIfContractIsNotSigned = (contract) => {
  let signedState = contract?.find(
    (item) => item.fieldName === "Assinado"
  )?.content;
  if (signedState !== "Sim") {
    return contract.filter((item) => item.fieldName !== "Data de assinatura");
  } else {
    return contract;
  }
};

const formatTypeHours = (data) => {
  if (!data || typeof data !== 'string') {
    return '';
  }

  const numbers = data.split("_").filter((item) => !isNaN(parseInt(item)));
  const firstNumber = numbers[0];
  const secondNumber = numbers[1];

  if (!firstNumber || !secondNumber) {
    return '';
  }

  return firstNumber + "X" + secondNumber;
};

export const formatHourPoolData = (data) => {
  if (!data || typeof data !== 'string') {
    return '';
  }

  try {
    let formattedData = data.split("_").join(" ").charAt(0).toUpperCase();
    formattedData += data
      .split("_")
      .filter((item) => isNaN(parseInt(item)))
      .join(" ")
      .slice(1);

    let hourRegime = formatTypeHours(data);
    if (hourRegime) {
      formattedData += " " + hourRegime;
    }

    return formattedData;
  } catch (error) {
    console.warn('Erro ao formatar dados de pool de horas:', error, 'Data:', data);
    return data || '';
  }
};

export const formatViewDateFields = (date, field) => {
  const checkValidDate = isValid(new Date(date));
  const fieldsWithNoNeedToFormat = [
    "Data de assinatura",
    "Data de cancelamento/rescisão",
    "Data inicial de serviço",
  ];
  if (fieldsWithNoNeedToFormat.find((item) => item === field)) {
    return date;
  } else if (checkValidDate) {
    return format(new Date(date), "dd/MM/yyyy");
  } else {
    return date.split(" ").shift();
  }
};
