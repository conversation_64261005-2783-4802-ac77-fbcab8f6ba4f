import { useMemo, useState } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Table,
  Button,
  Popconfirm,
  message,
} from "antd";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { DeleteOutlined } from "@ant-design/icons";
import { AddFunction } from "../../components/Modals/ManageFunctions/AddFunction";
import { dynamoGetById, dynamoPut } from "../../service/apiDsmDynamo";
import { EditFunction } from "../../components/Modals/ManageFunctions/EditFunction";
import axios from "axios";
import useSWR from "swr";
import { filterTableData } from "../../utils/filterTableData";
import { Counter } from "../../components/Counter";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";

export const ManageFunctions = () => {
  const { data, mutate } = useSWR(
    `${process.env.REACT_APP_STAGE}-page-actions`,
    async () => {
      try {
        const jwt = localStorage.getItem("jwt");

        const response = await axios.get(
          process.env.REACT_APP_API_PERMISSION + "read/all/0",
          {
            headers: {
              dynamodb: `${process.env.REACT_APP_STAGE}-page-actions`,
              Authorization: jwt,
            },
          }
        );

        // console.log('🔍 ManageFunctions: Dados brutos recebidos:', {
        //   dataStructure: response.data?.data,
        //   items: response.data?.data?.Items?.length || 0,
        //   firstItem: response.data?.data?.Items?.[0]
        // });

        let result = (response.data?.data?.Items || response.data?.Items || [])[0];

        // console.log('🔍 ManageFunctions: Dados finais para AddFunction:', {
        //   hasItems: !!(response.data?.data?.Items || response.data?.Items),
        //   itemsLength: (response.data?.data?.Items || response.data?.Items || []).length,
        //   hasResult: !!result,
        //   hasId: !!result?.id,
        //   id: result?.id,
        //   hasAll: !!result?.all,
        //   allLength: result?.all?.length || 0,
        //   structure: Object.keys(result || {})
        // });

        // Se não há nenhum item, criar estrutura padrão
        if (!result) {
          result = {
            id: `${process.env.REACT_APP_STAGE}-page-actions-default`,
            all: []
          };
        } else {
          // Se não tem ID, criar um ID padrão baseado no stage
          if (!result.id) {
            result.id = `${process.env.REACT_APP_STAGE}-page-actions-default`;
          }

          if (!result.all) {
            result.all = [];
          }
        }

        return result;
      } catch (error) {
        console.error('⚠️ ManageFunctions: Erro ao carregar dados:', error);
        return null;
      }
    }
  );

  const permissions = useSWR("manage_functions", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      // ✅ Verificação robusta da estrutura de dados
      if (data?.permissions) {
        const manageFunctionsPage = data.permissions.find((x) => x.page === "Gerenciar Funcionalidades");
        if (manageFunctionsPage?.actions) {
          return [...manageFunctionsPage.actions];
        }
      }

      throw new Error('Invalid permissions structure');
    } catch (error) {
      return [
        { code: "view_page" },
        { code: "view_edit" },
        { code: "view_delete" },
        { code: "create_function" },
        { code: "edit_function" },
        { code: "delete_function" },
        { code: "manage_function_permissions" }
      ];
    }
  });

  const { Content } = Layout;
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [loadingRemove, setLoadingRemove] = useState(false);

  if (!data && !loading) {
    return setLoading(true);
  }

  if (data && loading) {
    return setLoading(false);
  }

  const handleDelete = async ({ page }) => {
    setLoadingRemove(true);

    try {
      await dynamoPut(`${process.env.REACT_APP_STAGE}-page-actions`, data.id, {
        all: data.all.filter((p) => p.page !== page),
      });

      mutate({ all: data.all.filter((p) => p.page !== page) }, false);

      const username = localStorage.getItem("@dsm/username");
      const title = "Remoção de Página de Funcionalidades";
      const description = `${username} removeu todas as funcionalidades da página ${page}`;
      logNewAuditAction(username, title, description);

      message.success("Página removida com sucesso!");
    } catch (error) {
      console.log(error);
      message.error("Erro ao tentar remover página...");
    }

    setLoadingRemove(false);
  };

  const columns = [
    {
      code: "view_page",
      title: "Página",
      dataIndex: "page",
      key: "page",
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "edit",
      key: "edit",
      width: "1%",
      align: "center",
      render: (_, item) => {
        if (!data) {
          return <span>Carregando...</span>;
        }
        return (
          <EditFunction
            item={item}
            mutate={(data, options) => mutate(data, options)}
            permission={data}
          />
        );
      },
    },
    {
      code: "view_delete",
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      width: "1%",
      align: "center",
      render: (_, page) => {
        return (
          <Popconfirm
            title="Tem certeza de que quer remover essa página?"
            onConfirm={() => {
              handleDelete(page);
            }}
            placement="leftTop"
            cancelText="Não"
            okText="Sim"
          >
            <Button danger type="text" icon={<DeleteOutlined />} />
          </Popconfirm>
        );
      },
    },
  ];

  const filteredData = data?.all.filter((page) => {
    if (search === "") {
      return page;
    } else {
      return page.page.toLowerCase().includes(search.toLowerCase());
    }
  });
  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <Row justify="space-between">
              <Col
                span={8}
                style={{ marginBottom: "1em", borderRadius: "15px" }}
              >
                <Input
                  placeholder="Faça uma busca"
                  style={{
                    height: "35px",
                    borderRadius: "7px",
                    maxWidth: 300,
                  }}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </Col>
              {/* {permissions?.data
                ?.map((permission) => {
                  return permission.code;
                })
                .includes("add_function") && ( */}
              <Col>
                {data && data.id ? (
                  <AddFunction
                    permission={data}
                    mutate={(data, options) => mutate(data, options)}
                  />
                ) : (
                  <div>Carregando dados de permissões...</div>
                )}
              </Col>
              {/* )} */}
            </Row>
            <Counter tableData={filteredData} />
            <Table
              loading={loadingRemove || loading}
              dataSource={filteredData}
              columns={columns}
              scroll={{ x: 100 }}
              pagination={true}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
