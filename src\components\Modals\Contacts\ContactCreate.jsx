import { Button, Modal, Form, Input, message, Typography, Select } from "antd";
import { dynamoGetById, dynamoPut } from "../../../service/apiDsmDynamo";
import { MaskedInput } from "antd-mask-input";
import { useMemo, useState } from "react";
import {
  checksIfEmailAlreadyExists,
  contactCreationAuditLog,
  formatInsertContactArray,
  insertOTRSPerson,
} from "../Clients/controllers/clientUsersModal";

export const ContactCreateModal = (props) => {
  const { Text } = Typography;
  const { Option } = Select;
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState();
  const [triggerHelper, setTriggerHelper] = useState(false);
  const { client, getContacts, refreshContacts, addContactOptimistic, clients } = props;
  const [form] = Form.useForm();
  const contactTypes = [
    "Téc<PERSON><PERSON>",
    "Financeiro/Administrativo",
    "<PERSON>ere<PERSON>",
    "Diretor",
    "Sponsor",
    "Outro",
  ];

  const handleSubmit = async (data) => {
    let nonMaskedPhone = data.phone.replace(/[^A-Z0-9]+/gi, "");
    if (nonMaskedPhone.length < 10) return setTriggerHelper(true);
    setTriggerHelper(false);
    setLoading(true);

    try {
      const customer = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-customers`,
        client.id
      );
      let updatedClient = JSON.parse(JSON.stringify(customer));
      if (client.cnpj === "") updatedClient["cnpj"] = "-";
      const foundEmail = checksIfEmailAlreadyExists(clients, data, client.id);
      if (foundEmail) {
        message.warning("Esse e-mail já pertence a outro contato.");
        return setLoading(false);
      }

      const otrs = await insertOTRSPerson(client, data);
      const itsm_id = parseInt(otrs.data.person_id);

      data = formatInsertContactArray(client, data, itsm_id);
      updatedClient["contacts"].push(data);

      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-customers`,
        client.id,
        updatedClient
      );

      // ATUALIZAÇÃO OTIMÍSTICA IMEDIATA - Adicionar contato na lista local
      if (addContactOptimistic && typeof addContactOptimistic === 'function') {
        addContactOptimistic(data);
      }

      // Fechar modal imediatamente para feedback visual
      setShowModal(false);
      form.resetFields();
      message.success("Contato criado com sucesso!");

      // Aguardar propagação do DynamoDB (eventual consistency)
      await new Promise(resolve => setTimeout(resolve, 300));

      // Sincronização com banco
      try {
        if (getContacts) {
          await getContacts(true); // forceRefresh = true
        }
      } catch (error) {
        console.warn('⚠️ ContactCreate: Erro na sincronização:', error);
      }

      contactCreationAuditLog(data, client);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      message.error("Erro ao tentar criar contato.");
    }
  };

  const mask = useMemo(
    () => [
      {
        mask: "(00) 0 0000-0000",
        lazy: false,
      },
      {
        mask: "(00) 0000-0000",
        lazy: false,
      },
    ],
    []
  );

  return (
    <>
      <Button type="primary" onClick={() => setShowModal(true)}>
        Cadastrar contato
      </Button>
      <Modal
        title="Criar Contato"
        open={showModal}
        closable={false}
        onOk={() => form.submit()}
        okText="Criar"
        confirmLoading={loading}
        cancelText="Cancelar"
        onCancel={() => {
          setShowModal(false);
          form.resetFields();
        }}
      >
        <Form
          requiredMark={false}
          form={form}
          onFinish={handleSubmit}
          layout="vertical"
        >
          <Form.Item label="CRM ID" name="crm_id">
            <Input placeholder="CRM ID" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="first_name"
            label="Primeiro Nome"
          >
            <Input placeholder="Primeiro Nome" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="last_name"
            label="Último Nome"
          >
            <Input placeholder="Último Nome" />
          </Form.Item>
          <Form.Item
            rules={[
              { required: true, message: "E-mail inválido.", type: "email" },
            ]}
            name="email"
            label="Email"
          >
            <Input placeholder="Email" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="phone"
            label="Telefone"
            help={
              triggerHelper === true && (
                <Text type="danger">Telefone inválido</Text>
              )
            }
          >
            <MaskedInput
              style={{ border: triggerHelper === true && "solid 1px #ff0000" }}
              placeholder="Telefone"
              mask={mask}
              maskOptions={{
                dispatch: function (appended, dynamicMasked) {
                  const isCellPhone = dynamicMasked.unmaskedValue[2] === "9";
                  return dynamicMasked.compiledMasks[isCellPhone ? 0 : 1];
                },
              }}
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Selecione uma opção." }]}
            name="authorized_contact"
            label="Contato Autorizado"
          >
            <Select placeholder="Contato Autorizado">
              <Option value="Sim">Sim</Option>
              <Option value="Não">Não</Option>
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Selecione uma opção." }]}
            name="contact_type"
            label="Tipo de Contato"
          >
            <Select placeholder="Tipo de Contato">
              {contactTypes.map((type) => (
                <Option value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
