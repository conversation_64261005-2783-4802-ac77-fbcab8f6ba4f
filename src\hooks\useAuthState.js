/**
 * Hook para gerenciar estado de autenticação de forma otimizada
 * Integra cookies HttpOnly com fallback para localStorage
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { authService } from '../services/authService';
import { httpOnlyAuthService } from '../services/httpOnlyAuthService';
import { logger } from '../utils/logger';

export const useAuthState = () => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authConfig, setAuthConfig] = useState(null);
  const [error, setError] = useState(null);

  const initializationRef = useRef(false);
  const lastCheckRef = useRef(0);

  const initializeAuthState = useCallback(async () => {
    if (initializationRef.current) return;
    
    try {
      setIsLoading(true);
      setError(null);
      initializationRef.current = true;

      logger.debug('Inicializando estado de autenticação...');

      await authService.initialize();

      const config = authService.getAuthConfig();
      setAuthConfig(config);

      await authService.migrateFromLocalStorage();

      const authenticated = await authService.isAuthenticated();

      if (authenticated) {
        const userInfo = authService.getUserInfo();
        setUser(userInfo);
        setIsAuthenticated(true);
        
        logger.info('Estado de autenticação inicializado - usuário autenticado', {
          email: userInfo.email,
          method: authService.isUsingHttpOnlyCookies() ? 'cookies' : 'localStorage'
        });
      } else {
        setUser(null);
        setIsAuthenticated(false);
        logger.debug('Estado de autenticação inicializado - usuário não autenticado');
      }

    } catch (error) {
      logger.error('Erro na inicialização do estado de autenticação:', error);
      setError(error.message);
      setUser(null);
      setIsAuthenticated(false);
      setAuthConfig({ auth: { httpOnlySupported: false } });
    } finally {
      setIsLoading(false);
    }
  }, []);

  
  const login = useCallback(async (token, userInfo) => {
    try {
      setIsLoading(true);
      setError(null);

      logger.debug('Processando login...');

      const success = await authService.setToken(token, userInfo);

      if (success) {
        setUser(userInfo);
        setIsAuthenticated(true);
        
        logger.info('Login realizado com sucesso', {
          email: userInfo.email,
          method: authService.isUsingHttpOnlyCookies() ? 'cookies' : 'localStorage'
        });

        return { success: true };
      } else {
        throw new Error('Falha na autenticação');
      }
    } catch (error) {
      logger.error('Erro no login:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      logger.debug('Processando logout...');

      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);

      logger.info('Logout realizado com sucesso');
    } catch (error) {
      logger.error('Erro no logout:', error);
      setError(error.message);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Verificar autenticação
   */
  const checkAuth = useCallback(async (forceCheck = false) => {
    const now = Date.now();
    
    // Throttling: evita verificações muito frequentes
    if (!forceCheck && (now - lastCheckRef.current) < 5000) {
      return isAuthenticated;
    }

    try {
      lastCheckRef.current = now;
      
      const authenticated = await authService.isAuthenticated();

      if (authenticated !== isAuthenticated) {
        setIsAuthenticated(authenticated);
        
        if (authenticated) {
          const userInfo = authService.getUserInfo();
          setUser(userInfo);
        } else {
          setUser(null);
        }
      }

      return authenticated;
    } catch (error) {
      logger.error('Erro na verificação de autenticação:', error);
      return isAuthenticated; // Manter estado atual em caso de erro
    }
  }, [isAuthenticated]);

  /**
   * Atualizar informações do usuário
   */
  const updateUser = useCallback((newUserInfo) => {
    setUser(prevUser => {
      const updatedUser = { ...prevUser, ...newUserInfo };
      authService.setUserInfo(updatedUser);
      return updatedUser;
    });
  }, []);

  /**
   * Verificar permissão
   */
  const hasPermission = useCallback((requiredPermission) => {
    if (!user || !user.permission) return false;
    return user.permission === requiredPermission || user.permission === 'admin';
  }, [user]);

  /**
   * Obter informações de configuração
   */
  const getAuthInfo = useCallback(() => ({
    isUsingHttpOnlyCookies: authService.isUsingHttpOnlyCookies(),
    authConfig: authService.getAuthConfig(),
    method: authService.isUsingHttpOnlyCookies() ? 'cookies' : 'localStorage'
  }), []);

  useEffect(() => {
    initializeAuthState();
  }, [initializeAuthState]);

  return {
    user,
    isLoading,
    isAuthenticated,
    authConfig,
    error,
    login,
    logout,
    checkAuth,
    updateUser,
    hasPermission,
    getAuthInfo,
    isInitialized: initializationRef.current,
    isUsingHttpOnlyCookies: () => authService.isUsingHttpOnlyCookies(),
    getAuthConfig: () => authService.getAuthConfig(),
  };
};

export default useAuthState;
