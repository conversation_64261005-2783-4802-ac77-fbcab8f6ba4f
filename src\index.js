import "./utils/reactStrictModeSuppress";
import "./utils/suppressFindDOMNode";
import "./utils/antdWarningSuppress";
import "./utils/suppressPerformanceViolations";
import "./utils/preventNetworkErrors";
import dayjs from 'dayjs';
import 'dayjs/locale/pt-br';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import dayOfYear from 'dayjs/plugin/dayOfYear';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
dayjs.extend(dayOfYear);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

dayjs.locale('pt-br');


import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import "./styles/theme.css";
import "./styles/antd-custom.css";
import "./styles/layout-fixes.css";
import "./styles/table.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { store, persistor } from "./store/store";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { initializeStorageCleanup } from "./utils/clearCorruptedStorage";
import { initializePerformanceOptimizations } from "./utils/performanceOptimization";
import { initializeErrorSuppression } from "./utils/errorSuppression";
import { initializeDateCacheMonitoring } from "./utils/dateFilterCache";

initializeErrorSuppression();

import axios from 'axios';

if (process.env.REACT_APP_API_PERMISSION) {
  axios.defaults.baseURL = process.env.REACT_APP_API_PERMISSION;

  // Função para limpar localStorage e forçar login real
  window.forceRealLogin = () => {
    localStorage.clear();
    sessionStorage.clear();
    window.location.replace('http://localhost:3000/login');
  };

  // Criar botão de login real na página
  setTimeout(() => {
    if (localStorage.getItem('jwt')?.includes('dev-mock-token')) {
      const loginButton = document.createElement('button');
      loginButton.innerHTML = '� FAZER LOGIN REAL';
      loginButton.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: #ff4d4f;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      `;
      loginButton.onclick = window.forceRealLogin;
      document.body.appendChild(loginButton);


    }
  }, 3000);


}

// Interceptor para adicionar header WAF automaticamente para tokens mock
axios.interceptors.request.use(
  (config) => {
    // Se estiver usando token mock, adicionar header WAF
    const authHeader = config.headers?.Authorization;
    if (authHeader && authHeader.includes('dev-mock-token')) {
      config.headers['x-waf-header'] = process.env.REACT_APP_SWITCH_ROLES_WAF_HEADER;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);




initializeStorageCleanup();

initializePerformanceOptimizations();

initializeDateCacheMonitoring();

if (process.env.NODE_ENV === 'development') {
  window.showErrors = () => {
    const errors = JSON.parse(sessionStorage.getItem('errorLog') || '[]');
    return errors;
  };

  window.clearErrors = () => {
    sessionStorage.removeItem('errorLog');
  };
}

const container = document.getElementById("root");
const root = createRoot(container);

// Componente condicional para StrictMode
const AppWrapper = ({ children }) => {
  // Desabilitar StrictMode temporariamente para evitar warnings do findDOMNode do Antd
  // TODO: Reabilitar quando Antd for totalmente compatível com React 18
  const useStrictMode = process.env.REACT_APP_ENABLE_STRICT_MODE === 'true';

  if (useStrictMode) {
    return <React.StrictMode>{children}</React.StrictMode>;
  }

  return children;
};

root.render(
  <AppWrapper>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <App />
      </PersistGate>
    </Provider>
  </AppWrapper>
);

reportWebVitals();
