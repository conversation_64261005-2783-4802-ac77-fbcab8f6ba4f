import { createSlice } from "@reduxjs/toolkit";
import dayjs from "dayjs";
import { totalValuesInitialState } from "../../constants/totalValuesInitialState";

const REDUCER_NAME = "technicalProposal";

const INITIAL_STATE = {
  loading: false,
  readyToPersist: false,
  persistID: null,
  proposalID: null,
  projectName: "",
  clientName: "",
  type: "",
  status: "",
  contacts: [],
  architects: [],
  bus: [],
  opportunities: [],
  challenges: "",
  scenarios: "",
  premisses: "",
  extra_points: "",
  main_factors: "",
  additional_costs: [],
  calculator: [],
  expected_results: "",
  observations: "",
  internal_notes: "",
  architecture: "",
  selectedCustomer: "",
  selectedContact: "",
  selectedOpportunity: "",
  serviceList: [],
  selectedServices: [],
  changesServicesSelected: [],
  totalValues: totalValuesInitialState,
  pipedriveDealOwner: null,
  fileNames: [],
  architectureFileNames: [],
  scenarioFileNames: [],
  ticketData: null,
  commercialStatus: "não inicializada",
  active: true,
  monthSelected: "",
  spreadSetup: [
    {
      month: "12",
      value: 0,
    },
    {
      month: "24",
      value: 0,
    },
    {
      month: "36",
      value: 0,
    },
  ],
};

const technicalProposalSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setTechnicalProposalReduce(state, action) {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }
      state[action.payload.field] = value;
    },
    setTechnicalProposalCustomerReduce(state, action) {
      state.clientName = action.payload.clientName;
      state.selectedCustomer = action.payload.selectedCustomer;
    },
    setCalculatorItemTechnicalProposalReduce(state, action) {
      const index = action.payload.index;
      state.calculator[index] = action.payload.value;
    },

    setAdditionalCostsItemTechnicalProposalReduce(state, action) {
      const index = action.payload.index;
      state.additional_costs[index] = action.payload.value;
    },

    setAllTechnicalProposalReduce(state, action) {
      state.readyToPersist = action.payload.readyToPersist || false;
      state.persistID = action.payload.persistID;
      state.proposalID = action.payload.proposalID;
      state.projectName = action.payload.projectName;
      state.clientName = action.payload.clientName;
      state.type = action.payload.type;
      state.status = action.payload.status;
      state.contacts = action.payload.contacts;
      state.architects = action.payload.architects;
      state.bus = action.payload.bus;
      state.opportunities = action.payload.opportunities;
      state.challenges = action.payload.challenges;
      state.scenarios = action.payload.scenarios;
      state.premisses = action.payload.premisses;
      state.extra_points = action.payload.extra_points;
      state.main_factors = action.payload.main_factors;
      state.additional_costs = action.payload.additional_costs;
      state.calculator = action.payload.calculator;
      state.expected_results = action.payload.expected_results;
      state.observations = action.payload.observations;
      state.internal_notes = action.payload.internal_notes;
      state.architecture = action.payload.architecture;
      state.selectedCustomer = action.payload.selectedCustomer;
      state.selectedContact = action.payload.selectedContact;
      state.selectedOpportunity = action.payload.selectedOpportunity;
      state.serviceList = action.payload.serviceList;
      state.selectedServices = action.payload.selectedServices;
      state.changesServicesSelected = action.payload.changesServicesSelected;
      state.totalValues = action.payload.totalValues;
      state.fileNames = action.payload.fileNames;
      state.architectureFileNames = action.payload.architectureFileNames;
      state.scenarioFileNames = action.payload.scenarioFileNames;
      state.ticketData = action.payload.ticketData;
      state.commercialStatus = action.payload.commercialStatus;
      state.active = action.payload.active;
      state.monthSelected = action.payload.monthSelected;
      state.spreadSetup = action.payload.spreadSetup;
      state.pipedriveDealOwner = action.payload.pipedriveDealOwner;
      state.loading = false;
    },

    cleanTechnicalProposalReduce(state) {
      state.readyToPersist = false;
      state.persistID = null;
      state.proposalID = null;
      state.projectName = "";
      state.clientName = "";
      state.type = "";
      state.status = "";
      state.contacts = [];
      state.architects = [];
      state.bus = [];
      state.opportunities = [];
      state.challenges = "";
      state.scenarios = "";
      state.premisses = "";
      state.extra_points = "";
      state.main_factors = "";
      state.additional_costs = [];
      state.calculator = [];
      state.expected_results = "";
      state.observations = "";
      state.internal_notes = "";
      state.architecture = "";
      state.selectedCustomer = "";
      state.selectedContact = "";
      state.selectedOpportunity = "";
      state.serviceList = [];
      state.selectedServices = [];
      state.changesServicesSelected = [];
      state.totalValues = totalValuesInitialState;
      state.fileNames = [];
      state.architectureFileNames = [];
      state.scenarioFileNames = [];
      state.ticketData = null;
      state.commercialStatus = "não inicializada";
      state.active = true;
      state.monthSelected = "";
      state.spreadSetup = [];
      state.loading = false;
      state.pipedriveDealOwner = null;
    },
    setTechnicalProposalPersistInfoReduce(state, action) {
      state.persistID = action.payload.persistID;
      state.readyToPersist = action.payload.readyToPersist;
      state.serviceList = action.payload.serviceList;
    },
  },
});

export const {
  setTechnicalProposalReduce,
  setAllTechnicalProposalReduce,
  setTechnicalProposalCustomerReduce,
  cleanTechnicalProposalReduce,
  setCalculatorItemTechnicalProposalReduce,
  setAdditionalCostsItemTechnicalProposalReduce,
  setTechnicalProposalPersistInfoReduce,
} = technicalProposalSlice.actions;

export default technicalProposalSlice.reducer;
