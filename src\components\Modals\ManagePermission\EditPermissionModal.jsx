import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Row,
  Col,
  Form,
  Input,
  Divider,
  Select,
  Table,
  Tag,
  Popconfirm,
  message,
} from "antd";
import { EditOutlined, DeleteOutlined, DeleteRowOutlined, DeleteFilled } from "@ant-design/icons";
import { dynamoGet, dynamoPut } from "../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import { RequiredLabelForm } from "../../RequiredLabelForm";

export const EditPermissionModal = ({ getPermission, item }) => {
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState();
  const [selectedPageFunctions, setSelectedPageFunctions] = useState([]);
  const [pageSelection, setPageSelection] = useState();
  const [functionSelected, setFunctionSelected] = useState();
  const [disable, setDisable] = useState(false);
  const [dataTabela, setDataTabela] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [list, setList] = useState([]);
  const [namePermission, setNamePermission] = useState("");

  const getPermissionData = async (dataTabela) => {
    const permission = await dynamoGet(
      `${process.env.REACT_APP_STAGE}-page-actions`
    );
    setPermissions(permission[0]?.all);
    setList(
      permission[0]?.all?.filter((existingPage) => {
        return !dataTabela.permissions.some((page) => {
          return page.page === existingPage.page;
        });
      })
    );
  };

  const [form] = Form.useForm();
  const { Option } = Select;

  const getActions = (namePage) => {
    let actions;
    permissions?.forEach(({ page, permissions }) => {
      if (page === namePage) {
        actions = [...permissions];
      }
    });
    return actions;
  };

  function setPage({ key: id, value: page }) {
    setPageSelection({ id, page });
    setSelectedPageFunctions(getActions(page));
  }

  const clearFormAndSets = () => {
    setPageSelection("");
    setFunctionSelected("");
    form.resetFields();
    form.setFieldsValue({ name_permission: namePermission });
  };

  function AddPermission(value) {
    dataTabela.map((currentTablePages) => {
      setList(list.filter((item) => item.page !== currentTablePages.page));
    });
    let actions;

    if (!pageSelection) {
      message.error("Preencha os campos");
      return;
    }

    if (value.target.textContent === "Adicionar todas as ações da página") {
      actions = [...selectedPageFunctions];
    } else if (!functionSelected) {
      message.error("Preencha os campos");
      return;
    } else {
      actions = functionSelected.map(({ key: action, value: code }) => ({
        action,
        code,
      }));
    }

    const objDataTable = {
      ...pageSelection,
      actions,
    };

    setDataTabela((dataTabela) => [...dataTabela, objDataTable]);
    setList(
      list.filter((currentPage) => currentPage.page !== objDataTable.page)
    );
    clearFormAndSets();
  }

  const DeleteItem = ({ page }) => {
    const newTable = dataTabela.filter((row) => row.page !== page);
    const pagesTable = newTable.map(({ page }) => page);
    setList(permissions.filter(({ page }) => !pagesTable.includes(page)));
    setDataTabela(newTable);
  };

  const columns = [
    {
      title: "Página",
      dataIndex: "page",
      key: "page",
    },
    {
      title: "Funcionalidades",
      dataIndex: "actions",
      key: "actions",
      render: (permissions) => (
        <>
          {permissions?.map(({ code }) => {
            return <Tag key={code}>{code}</Tag>;
          })}
        </>
      ),
    },
    {
      title: "Excluir",
      dataIndex: "delete",
      key: "delete",
      render: (_, key) => {
        return (
          <Popconfirm
            okText="Sim"
            cancelText="Não"
            title="Tem certeza que deseja excluir esta permissão?"
            placement="bottom"
            onConfirm={() => DeleteItem(key)}
          >
            <Button danger type="danger">
              <DeleteFilled />
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const setItems = () => {
    form.setFieldsValue({
      name_permission: item.name_permission,
    });
    setNamePermission(item.name_permission);
    setDataTabela(item.permissions);
    getPermissionData(item);
  };

  const createDadosBanco = () => {
    const permissions = dataTabela.map(({ id, page, actions }) => {
      return {
        page,
        actions,
      };
    });
    return {
      name_permission: namePermission,
      permissions,
    };
  };

  const handleSubmit = async () => {
    setLoading(true);
    const dados = createDadosBanco();
    try {
      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-permissions`,
        item.id,
        dados
      );

      const username = localStorage.getItem("@dsm/username");
      const title = "Edição de Permissão";
      const description = `${username} editou a permissão: '${dados.name_permission}'`;
      logNewAuditAction(username, title, description);
      message.success("Permissão editada com sucesso!");
    } catch (error) {
      console.log(error);
      message.error(
        "Ocorreu um erro ao tentar editar a permissão, tenta novamente."
      );
    }
    getPermission();
    form.resetFields();
    setDataTabela([]);
    setShow(false);
    setLoading(false);
  };
  return (
    <>
      <Button
        type="text"
        onClick={() => {
          setItems();
          setShow(true);
        }}
      >
        <EditOutlined />
      </Button>
      <Modal
        title="Editar uma permissão"
        open={show}
        confirmLoading={loading}
        onOk={() => {
          form.submit();
        }}
        onCancel={() => {
          setShow(false);
          setDataTabela([]);
          form.resetFields();
          setList(
            permissions.map(({ page, id }) => {
              return { id, page };
            })
          );
        }}
        okText="Atualizar"
        cancelText="Cancelar"
      >
        <Row justify="center">
          <Col span={24}>
            <Form
              form={form}
              requiredMark={false}
              layout="vertical"
              onFinish={handleSubmit}
            >
              <Form.Item
                label={<RequiredLabelForm title="Nome da permissão" />}
                name="name_permission"
                rules={[
                  {
                    required: true,
                    message: "Preencha esse o nome da permissão.",
                  },
                ]}
              >
                <Input
                  placeholder="Nome da permissão"
                  onChange={({ target: { value } }) => setNamePermission(value)}
                />
              </Form.Item>

              <Divider />

              <Form.Item
                name="page"
                label="Selecione uma página para liberar acesso"
              >
                <Select
                  placeholder="Selecione uma página"
                  onChange={(_, values) => {
                    setPage(values);
                    form.setFieldsValue({ actions: undefined });
                  }}
                >
                  {list.map(({ id, page }) => {
                    return (
                      <Option value={page} key={id}>
                        {page}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>

              <Form.Item
                name="actions"
                label="Selecione as permissões para liberar acesso"
              >
                <Select
                  mode="multiple"
                  onChange={(_, values) => setFunctionSelected(values)}
                  placeholder="Selecione uma ação"
                >
                  {selectedPageFunctions.map(({ id, action, code }) => {
                    return (
                      <Option disabled={disable} value={code} key={action}>
                        {action}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              <Row justify="space-between">
                <Col>
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={AddPermission}
                      block
                      icon={<EditOutlined />}
                    >
                      Adicionar todas as ações da página
                    </Button>
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={AddPermission}
                      block
                      icon={<EditOutlined />}
                    >
                      Adicionar Permissão
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
            <Table
              scroll={{ x: "auto" }}
              dataSource={dataTabela}
              columns={columns}
            />
          </Col>
        </Row>
      </Modal>
    </>
  );
};
