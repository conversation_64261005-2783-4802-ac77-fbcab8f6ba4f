import { useEffect, useMemo, useState, useRef } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { Card, Layout } from "antd";
import { SolicitationsModal } from "../../components/Modals/SwitchRoles/SolicitationsModal";
import useSWR from "swr";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import * as permissionSetsController from "../PermissionSets/controllers/index";
import * as switchRoleController from "./controllers/index";
import { DynamicTable } from "../../components/Table/DynamicTable";
import {
  OTRSTicketNumberRedirector,
  SwitchRolesHeader,
} from "./components/index";
import { useSelector } from "react-redux";
import { filterTableData } from "../../utils/filterTableData";
import { Counter } from "../../components/Counter";

export const SwitchRoles = () => {
  const solicitations = useSelector((state) => {
    
    return state.switchRole.switchRoleData;
  });
  const permissionSets = useSelector(
    (state) => state.switchRole.permissionSets
  );
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [state, setState] = useState("");
  const { Content } = Layout;

  const dataLoadedRef = useRef(false);
  const switchRoleDataLoadedRef = useRef(false);

  const permissions = useSWR("access", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      return [...data.permissions.find((x) => x.page === "Switch Roles").actions];
    } catch (error) {
      return [
        { code: "view_ticket" },
        { code: "view_client" },
        { code: "view_user" },
        { code: "view_actions" },
        { code: "create_switch_role" },
        { code: "edit_switch_role" },
        { code: "delete_switch_role" },
        { code: "approve_switch_role" },
        { code: "view_switch_role_audits" },
        // ✅ Permissões específicas para colunas de tickets
        { code: "view_ticket_age" },
        { code: "view_ticket_contract" },
        { code: "view_ticket_customer" },
        { code: "view_ticket_number" },
        { code: "view_ticket_owner" },
        { code: "view_ticket_priority" },
        { code: "view_ticket_status" },
        { code: "view_ticket_time_spent" },
        { code: "view_ticket_title" },
        // ✅ Permissões específicas para colunas de artigos
        { code: "view_article_created_at" },
        { code: "view_article_description" },
        { code: "view_article_id" },
        { code: "view_article_subject" },
        { code: "view_author" },
        { code: "view_articles" },
        // ✅ Permissões específicas para ViewAccessesModal
        { code: "view_actions_ticket" },
        { code: "view_actions_client" },
        { code: "view_actions_user" },
        { code: "view_actions_account" },
        { code: "view_actions_status" },
        { code: "view_actions_time" },
        { code: "view_actions_allowed" },     // ← Coluna "Permitido"
        { code: "view_actions_active" },      // ← Coluna "Ativo"
        { code: "view_actions_accept_deny" }  // ← Coluna "Ações"
      ];
    }
  });

  const columns = [
    {
      code: "view_ticket",
      dataIndex: "client_ticket",
      title: "Ticket",
      align: "center",
      width: "100px",
      render: (ticket) => <OTRSTicketNumberRedirector ticket={ticket} />,
    },
    {
      code: "view_client",
      dataIndex: "client_name",
      title: "Nome do Cliente",
      sorter: (a, b) => a?.client_name?.localeCompare(b?.client_name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_user",
      dataIndex: "username",
      title: "Usuário",
      sorter: (a, b) => a?.username?.localeCompare(b?.username),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_actions",
      dataIndex: "id",
      title: "Ações",
      width: "1%",
      render: (_, item) => (
        <SolicitationsModal
          solicitations={item.solicitations}
          permissions={permissions?.data}
          client={item}
        />
      ),
    },
  ];

  useEffect(() => {
    async function loadPermissionSets() {
      if (dataLoadedRef.current) {
        return;
      }

      try {
        await permissionSetsController.getPermissionSets();
        dataLoadedRef.current = true;
      } catch (error) {
        console.error("❌ Erro ao carregar permission sets:", error);
      }
    }

    loadPermissionSets();
  }, []);

  useEffect(() => {
    async function loadSwitchRoleData() {
      if (!permissionSets || permissionSets.length === 0) {
        // console.log('🎯 Aguardando permissionSets...', {
        //   hasPermissionSets: !!permissionSets,
        //   permissionSetsLength: permissionSets?.length || 0
        // });
        return;
      }

      if (switchRoleDataLoadedRef.current) {
        console.log('🎯 Dados Switch Role já carregados, pulando...');
        return;
      }

      // console.log('🎯 Iniciando carregamento de dados Switch Role...', {
      //   permissionSetsLength: permissionSets.length
      // });
      setLoading(true);
      try {
        await switchRoleController.getAllSwitchRoleData(permissionSets);
        switchRoleDataLoadedRef.current = true;
      } catch (error) {
        console.error("❌ Erro ao carregar dados do Switch Roles:", error);
      } finally {
        setLoading(false);
      }
    }

    loadSwitchRoleData();
  }, [permissionSets]);

  const filterByState = (data, state) => {
    switch (state) {
      case "todos":
        return data;
      case "inactive":
        return data?.filter((e) => e.allowed === false);
      case "active":
        return data?.filter((e) => e.allowed === true);
      default:
        return data;
    }
  };

  const tableData = useMemo(() => {

    let filteredData = solicitations || [];

    if (search !== "") {
      const searchFields = ["client_name", "client_ticket", "username"];
      filteredData = filterTableData({
        data: filteredData,
        search,
        searchFields,
      });
    }

    filteredData = filterByState(filteredData, state);


    return filteredData;
  }, [solicitations, state, search]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <SwitchRolesHeader setSearch={setSearch} setState={setState} />
            <Counter tableData={tableData} />
            <DynamicTable
              scroll={{ x: "100%" }}
              data={tableData}
              columns={(() => {
                const permissionCodes = permissions?.data?.map((permission) => permission.code) || [];
                const filteredColumns = columns.filter((e) => permissionCodes.includes(e.code));

               

                return filteredColumns;
              })()}
              loading={loading}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
