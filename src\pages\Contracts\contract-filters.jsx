import "moment/locale/pt-br";
import React, { useEffect, useState } from "react";
import { useSelector, shallowEqual } from "react-redux";
import { Row, Col, Space, Select, Typography, Button, message } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import { setContractFieldState } from "../../store/actions/contract-action";
import * as controller from "../../controllers/contracts/contract-controller";
import { SearchInput } from "../../components/SearchInput";
import { PipedriveIntegration } from "../../components/Modals/Contracts/PipedriveIntegration";
import { AddContractModal } from "../../components/Modals/Contracts/AddContractModal";
import { contractsView } from "../../constants/contractsInfoPerPermission";
import { ConsumptionTypeFilter } from "./components/consumption-type-filter";
import { DateFilters } from "./components/date-filter";
import { SquadSelectorField } from "./components/squad-filter";
import { PoolTypeFilter } from "./components/pool-type-filter";
import { activeInactiveSelectOptions } from "../../constants/activeInactiveSelectOptions";
import { ExecutiveFilter } from "./components/executive-filter";

const { Text } = Typography;

export const ContractFilters = () => {
  const [loading, setloading] = useState(false);
  const [contractReportPermission, setContractReportPermission] =
    useState(false);
  const [pipedriveIntegrationPermission, setPipedriveIntegrationPermission] =
    useState(false);
  const [createContractPermission, setCreateContractPermission] =
    useState(false);
  const { permissions, filteredContracts } = useSelector(
    (state) => state.contract,
    shallowEqual
  );

  async function handleContratReport() {
    setloading(true);
    try {
      const allContracts = await controller.getAllContracts();
      const formattedContracts = await controller.getContractInfoByPermission(
        permissions,
        filteredContracts,
        allContracts,
        contractsView
      );
      const blob = await controller.generateExcel(formattedContracts);
      controller.downloadExcel(blob, "Relatório de Contratos");
      message.success("Arquivo gerado com sucesso!");
    } catch (error) {
      message.error("Erro ao gerar arquivo.");
    }
    setloading(false);
  }

  function handlePermissions() {
    const permissionCodes = permissions.map((permission) => permission.code);

    const pipedriveIntegrationPermission = permissionCodes.includes(
      "pipedrive_integration"
    );

    const createContractPermission =
      permissionCodes.includes("create_contract");

    const contractReportPermission = permissions?.some((permission) =>
      contractsView.some((obj) => obj.permissionCode === permission.code)
    );

    setPipedriveIntegrationPermission(pipedriveIntegrationPermission);
    setCreateContractPermission(createContractPermission);
    setContractReportPermission(contractReportPermission);
  }

  useEffect(() => {
    handlePermissions();
  }, [permissions]);

  return (
    <>
      <Row justify="end" style={{ marginBottom: "1em" }}>
        {createContractPermission && (
          <Col span={3.5}>
            <Space>
              <AddContractModal />
            </Space>
          </Col>
        )}
        {pipedriveIntegrationPermission && (
          <Col style={{ marginLeft: "12px" }} span={4.5}>
            <PipedriveIntegration />
          </Col>
        )}
        {contractReportPermission && (
          <Col style={{ marginLeft: "12px" }}>
            <Button
              type="primary"
              loading={loading}
              style={{ cursor: "pointer" }}
              disabled={!filteredContracts.length}
              onClick={async () => handleContratReport()}
            >
              <DownloadOutlined />
              Exportar Relatório
            </Button>
          </Col>
        )}
      </Row>
      <Row
        justify="space-between"
        style={{ marginBottom: "1em", alignItems: "end" }}
      >
        <Col span={8}>
          <Text>Filtrar:</Text>
          <SearchInput
            style={{
              height: "34px",
              borderRadius: "7px",
              lineHeight: "34px",
            }}
            placeholder="Buscar contratos..."
            onChange={(value) =>
              setContractFieldState({
                field: "search",
                value: value,
              })
            }
          />
        </Col>
        <SquadSelectorField span={8} />
        <DateFilters />
      </Row>
      <Row
        justify="space-between"
        style={{ marginBottom: "1em", alignItems: "end" }}
      >
        <ExecutiveFilter span={8} />
        <PoolTypeFilter span={5} />
        <Col span={5}>
          <Text>Status:</Text>
          <Select
            onChange={(e) => {
              setContractFieldState({
                field: "active",
                value: e,
              });
            }}
            style={{ width: "100%" }}
            defaultValue="todos"
            options={activeInactiveSelectOptions}
          ></Select>
        </Col>
        <ConsumptionTypeFilter span={5} />
      </Row>
    </>
  );
};
