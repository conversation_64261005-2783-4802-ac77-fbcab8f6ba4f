import axios from "axios";
import { authService } from "../services/authService";
import { getApiUrl } from "../utils/devConfig";

export const dsmApiProvider = (jwt = "") => {
  const token = jwt ? jwt : localStorage.getItem("jwt");

  const instance = axios.create({
    baseURL: process.env.REACT_APP_API_PERMISSION || "",
    headers: {
      Authorization: `${token}`,
    },
  });

  instance?.interceptors?.request.use((config) => {
    if (
      !config.headers.Authorization ||
      config.headers.Authorization.length < 15
    ) {
      const token = jwt ? jwt : localStorage.getItem("jwt");
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  });

  return instance;
};

export const dsmApiProviderDynamic = async (jwt = "") => {
  const apiUrl = await getApiUrl();

  if (jwt) {
    return axios.create({
      baseURL: apiUrl,
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    });
  }

  // Caso contrário, usa o serviço de autenticação sem credentials
  return authService.createSimpleAxios(apiUrl);
};

export function getDsmHeader(tableName = "", jwt = "") {
  const token = jwt ? jwt : localStorage.getItem("jwt");

  let headers = {
    Authorization: `Bearer ${token}`,
  };

  if (tableName) {
    headers["dynamodb"] = tableName;
  }

  return headers;
}
