import { dsmApi<PERSON><PERSON>ider } from "../../../provider/dsm-api-provider";
import { setSwitchRoleState } from "../../../store/actions/switch-role-action";
import { uniqByKeepLast } from "../../../utils/uniqueByKeepLast";

export const getAllSwitchRoleData = async (permissionSets) => {
  const provider = dsmApiProvider();
  let solicitations = [];
  try {
    let { data } = await provider.get("read/switch-role");

    let Items = [];
    if (data.data && data.data.Items) {
      Items = data.data.Items;
    } else if (data.Items) {
      Items = data.Items;
    } else if (Array.isArray(data.data)) {
      Items = data.data;
    } else if (Array.isArray(data)) {
      Items = data;
    }

    solicitations = formatTableData(Items);

    solicitations.forEach((e) => {
      for (let account of permissionSets) {
        if (e.username === account.user) {
          e.arn = account.arn;
        }
      }
    });

    let tkt = [];

    // Criar um mapa para agrupar por client_user
    const groupedByClientUser = {};
    solicitations.forEach(item => {
      const key = item.client_user;
      if (!groupedByClientUser[key]) {
        groupedByClientUser[key] = [];
      }
      groupedByClientUser[key].push(item);
    });

    Object.keys(groupedByClientUser).forEach((clientUser) => {
      const userSolicitations = groupedByClientUser[clientUser];
      const firstItem = userSolicitations[0];

      let obj = {
        username: firstItem.username,
        client_ticket: firstItem.client_ticket,
        client_name: firstItem.client_name,
        allowed: userSolicitations.every((v) => v.allowed),
        solicitations: userSolicitations
      };

      tkt.push(obj);
    });

   

    setSwitchRoleState({ field: "switchRoleData", value: tkt });
  } catch (err) {
    throw err;
  }
};

const formatTableData = (data) => {
  let formattedData = [];
  data.forEach((e) => {
    formattedData.push({
      id: e.id,
      role: e.role,
      time: e.time ? e.time : e.requestedTime,
      arn: e.arn,
      active: e.active,
      allowed: e.allowed,
      username: e.username,
      ticket_id: e.ticket_id,
      account_id: e.account_id,
      updated_at: e.updated_at,
      client_name: e?.client_name?.replace(/(^\w{1})|(\s+\w{1})/g, (letter) =>
        letter.toUpperCase()
      ),
      client_ticket: e.client_ticket,
      client_user: e?.client_ticket?.toString() + e.username,
    });
  });

  return formattedData;
};
