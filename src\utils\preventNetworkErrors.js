/**
 * Previne erros de rede de chegarem ao React Error Boundary
 */

import axios from 'axios';

// Interceptor global para capturar e tratar erros de rede
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const is404Error = error.response?.status === 404;
    const is500Error = error.response?.status === 500;

    const isMalformedUrl =
      error.config?.url?.includes('/devread/') ||
      error.config?.url?.includes('/devupdate/') ||
      error.config?.url?.includes('/devdelete/') ||
      error.config?.url?.includes('/devswitch-role') ||
      error.config?.url?.includes('/devcreate') ||
      error.config?.url?.includes('/devcontracts') ||
      error.config?.url.includes('/devemail') ||
      error.config?.url.includes('/devstate-machine/') ||
      error.config?.url.includes('/devstate-machine') ||
      error.config?.url.includes('/devread/audits') ||
      error.config?.url.includes('/devread/paginate') ||
      error.config?.url.includes('/devemail/send-email') ||
      error.config?.url.includes('/devcontracts') ||
      error.config?.url?.includes('/devs3/');

    // Tratar erros 500 de forma mais robusta
    if (is500Error) {
      console.error('🚨 Erro 500 interceptado:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        message: error.message,
        data: error.response?.data
      });

      return Promise.reject(error);
    }

    // Apenas suprimir URLs malformadas, não 404s de APIs válidas
    if (isMalformedUrl) {
      // Retornar uma resposta mock para evitar quebrar a aplicação
      return Promise.resolve({
        data: { data: { Items: [] } },
        status: 200,
        statusText: 'OK (Mock Response - URL Issue)',
        headers: {},
        config: error.config
      });
    }

    if (is404Error) {

    }

    return Promise.reject(error);
  }
);

// Interceptor para requisições - garantir URLs corretas
axios.interceptors.request.use(
  (config) => {
    if (config.url) {
      const fixes = [
        { from: '/devread/', to: '/dev/read/' },
        { from: '/devupdate/', to: '/dev/update/' },
        { from: '/devdelete/', to: '/dev/delete/' },
        { from: '/devswitch-role', to: '/dev/read/switch-role' },
        { from: '/devcustomers/', to: '/dev/customers/' },
        { from: '/devauth/', to: '/dev/auth/' },
        { from: '/devcognito/', to: '/dev/cognito/' },
        { from: '/devhealth', to: '/dev/health' },
        { from: '/devcreate', to: '/dev/create' },
        { from: '/devs3/', to: '/dev/s3/' },
        { from: 'devread/', to: 'dev/read/' },
        { from: 'devupdate/', to: 'dev/update/' },
        { from: 'devdelete/', to: 'dev/delete/' },
        { from: 'devswitch-role', to: 'dev/read/switch-role' },
        { from: 'devcustomers/', to: 'dev/customers/' },
        { from: 'devauth/', to: 'dev/auth/' },
        { from: 'devcognito/', to: 'dev/cognito/' },
        { from: 'devhealth', to: 'dev/health' },
        { from: 'devcreate', to: 'dev/create' },
        { from: 'devs3/', to: 'dev/s3/' },
        { from: 'devread/audits', to: 'dev/read/audits' },
        { from: 'devread/paginate', to: 'dev/read/paginate' },
        { from: 'devemail/send', to: 'dev/email/send-email'},
        { from: 'devcontracts', to: 'dev/contracts'},
        { from: 'devstate-machine/', to: 'dev/state-machine/' },
        { from: 'devstate-machine', to: 'dev/state-machine' },
        { from: 'devs3/', to: 'dev/s3/' }
      ];

      for (const fix of fixes) {
        if (config.url.includes(fix.from)) {
          config.url = config.url.replace(fix.from, fix.to);

        }
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Sobrescrever console.error temporariamente para suprimir erros específicos
const originalConsoleError = console.error;
console.error = (...args) => {
  const message = args.join(' ');
  
  if (
    message.includes('Network Error') ||
    message.includes('ERR_FAILED') ||
    message.includes('CORS policy') ||
    message.includes('XMLHttpRequest') ||
    message.includes('devread') ||
    message.includes('devupdate') ||
    message.includes('devdelete') ||
    message.includes('devcontracts') ||
    message.includes('devemail') ||
    message.includes('devswitch-role') ||
    message.includes('devstate-machine') ||
    message.includes('devs3')
  ) {
    return;
  }
  
  originalConsoleError.apply(console, args);
};

// Interceptar XMLHttpRequest para corrigir URLs malformadas
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url, ...args) {
  // Corrigir URL se necessário
  if (typeof url === 'string') {
    const fixes = [
      { from: '/devread/', to: '/dev/read/' },
      { from: '/devupdate/', to: '/dev/update/' },
      { from: '/devdelete/', to: '/dev/delete/' },
      { from: '/devswitch-role', to: '/dev/read/switch-role' },
      { from: '/devcognito/', to: '/dev/cognito/' },
      { from: '/devhealth', to: '/dev/health' },
      { from: '/devcreate', to: '/dev/create' },
      { from: '/devs3/', to: '/dev/s3/' },
      { from: 'devread/', to: 'dev/read/' },
      { from: 'devupdate/', to: 'dev/update/' },
      { from: 'devdelete/', to: 'dev/delete/' },
      { from: 'devswitch-role', to: 'dev/read/switch-role' },
      { from: 'devcognito/', to: 'dev/cognito/' },
      { from: 'devhealth', to: 'dev/health' },
      { from: 'devcreate', to: 'dev/create' },
      { from: 'devs3/', to: 'dev/s3/' },
      // Correções adicionais para URLs malformadas
      { from: 'devread/audits', to: 'dev/read/audits' },
      { from: 'devread/paginate', to: 'dev/read/paginate' },
      { from: 'devemail/send', to: 'dev/email/send-email'},
      {from: 'devcontracts', to: 'dev/contracts'},
      { from: 'devstate-machine/', to: 'dev/state-machine/' },
      { from: 'devstate-machine', to: 'dev/state-machine' },
      { from: 'devs3/', to: 'dev/s3/' }
    ];

    for (const fix of fixes) {
      if (url.includes(fix.from)) {
        url = url.replace(fix.from, fix.to);
      }
    }
  }

  return originalXHROpen.call(this, method, url, ...args);
};

// Interceptar erros do XMLHttpRequest de forma mais eficiente
const originalXHRSend = XMLHttpRequest.prototype.send;
XMLHttpRequest.prototype.send = function(...args) {
  // Usar requestIdleCallback para evitar forced reflow
  const handleError = (event) => {
    if (typeof requestIdleCallback === 'function') {
      requestIdleCallback(() => {
        event.stopPropagation();
      });
    } else {
      setTimeout(() => {
        event.stopPropagation();
      }, 0);
    }
  };

  const handleLoadEnd = () => {
    if (this.status === 0 || this.status >= 400) {
      // Log apenas se necessário, sem manipular DOM
    }
  };

  this.addEventListener('error', handleError, { passive: true });
  this.addEventListener('loadend', handleLoadEnd, { passive: true });

  return originalXHRSend.apply(this, args);
};



export default {
  restore: () => {
    console.error = originalConsoleError;

  }
};
