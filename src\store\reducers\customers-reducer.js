import { createSlice } from "@reduxjs/toolkit";
import moment from "moment";
import dayjs from "dayjs";

const REDUCER_NAME = "customers";

export const INITIAL_STATE = {
  activeCustomersWithActiveContract: [],
  activeCustomersWithInactiveContract: [],
  inactiveCustomers: [],
  prospects: [],
  allCustomers: [],
  state: "ativosA",
  search: "",
  dtEnd: null,
  dtStart: null,
};

const customersSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setActiveCustomersWithActiveContractsReduce(state, action) {
      state.activeCustomersWithActiveContract = action.payload;
    },
    setActiveCustomersWithInactiveContractsReduce(state, action) {
      state.activeCustomersWithInactiveContract = action.payload;
    },
    setInactiveCustomersReduce(state, action) {
      state.inactiveCustomers = action.payload;
    },
    setProspectsReduce(state, action) {
      state.prospects = action.payload;
    },
    setAllCustomersReduce(state, action) {
      state.allCustomers = action.payload;
    },
    setStateReduce(state, action) {
      state.state = action.payload;
    },
    setSearchReduce(state, action) {
      state.search = action.payload;
    },
    setDateReduce(state, action) {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }
      state[action.payload.field] = value;
    },
  },
});

export const {
  setActiveCustomersWithActiveContractsReduce,
  setActiveCustomersWithInactiveContractsReduce,
  setInactiveCustomersReduce,
  setProspectsReduce,
  setAllCustomersReduce,
  setStateReduce,
  setSearchReduce,
  setDateReduce,
} = customersSlice.actions;

export default customersSlice.reducer;
