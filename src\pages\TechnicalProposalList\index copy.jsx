import React, { use<PERSON>ontext, useEffect, useMemo, useState } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Space,
  Input,
  Typography,
  Table,
  Popconfirm,
  Button,
  Select,
  Tag,
  message,
} from "antd";
import {
  LoadingOutlined,
  EditOutlined,
  PlusOutlined,
  FileOutlined,
} from "@ant-design/icons";
import Cookies from "js-cookie";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { NavLink, useNavigate } from "react-router-dom";
import { ProposalInfo } from "../../components/Modals/TechnicalProposals/ProposalInfo";
import { dynamoGet, dynamoPut } from "../../service/apiDsmDynamo";
import { clearLocalStorageItems } from "../../constants/clearLocalStorageItems";
import { filterTableData } from "../../utils/filterTableData";
import { DataCookieContext } from "../../contexts/data-cookie";
import { store } from "../../store/store";
import { AlertProposalInProgress } from "../TechnicalProposalAdd/components/alert-proposal-in-progress";
import {
  deletePersistData,
  removeAllDataFromLocalStorage,
} from "../../controllers/Proposals/proposal-controller";
import { cleanTechnicalProposalState } from "../../store/actions/technical-proposal-action";
import { Counter } from "../../components/Counter";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";

export const TechnicalProposalList = () => {
  const navigate = useNavigate();
  const { Content } = Layout;
  const { Option } = Select;
  const [collapsed, setCollapsed] = useState(false);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [allProposals, setAllProposals] = useState([]);
  const [actionsState, setActionsState] = useState("todas");
  const { Text } = Typography;

  const [loadingButtons, setLoadingButtons] = useState(false);

  function getCustomerName(item) {
    let clientName = "";

    if (item.customer.names) {
      if (item.customer.names.fantasy_name) {
        clientName = item.customer.names.fantasy_name;
      } else {
        clientName = item.customer.names.name;
      }
    }

    if (item.customer.name) {
      clientName = item.customer.name;
    }

    return clientName;
  }

  useEffect(() => {
    window.scrollTo(0, 0);
    setLoading(true);
    clearLocalStorageItems.forEach((item) => localStorage.removeItem(item));
    localStorage.setItem("technical-proposal/services", "");
    localStorage.setItem("technical-proposal/form", "");
    localStorage.setItem("technical-proposal/customer", "");
    localStorage.setItem("technical-proposal/fileList", "");
    localStorage.setItem("technical-proposal/architectureFileList", "");
    localStorage.setItem("technical-proposal/mainSearchId", "");
    localStorage.setItem("oportunities", "");
    localStorage.setItem("CollapseValidator", "");
    dynamoGet(`${process.env.REACT_APP_STAGE}-proposals`)
      .then((data) => {
        setAllProposals(data);
        setLoading(false);
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  async function handleGoTo(url, item) {
    setLoadingButtons(true);

    await deletePersistData();
    removeAllDataFromLocalStorage();
    cleanTechnicalProposalState();

    setLoading(false);

    navigate(url);
  }

  const columns = [
    {
      title: "Nome do Projeto",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.name.localeCompare(b?.name),
    },
    {
      title: "Cliente",
      dataIndex: "customer",
      key: "customer",
      sorter: (a, b) => getCustomerName(a).localeCompare(getCustomerName(b)),
      render: (id, item) => getCustomerName(item),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      align: "center",
      sorter: (a, b) => a?.status?.localeCompare(b?.status),
      render: (id, item) => {
        if (item.status === "concluída") {
          return (
            <Tag style={{ color: "white" }} color={"#0F9347"}>
              Concluída
            </Tag>
          );
        } else if (item.status === "em andamento") {
          return (
            <Tag style={{ color: "white" }} color={"#FFC300"}>
              Em Andamento
            </Tag>
          );
        } else if (item.status === "não inicializada") {
          return (
            <Tag style={{ color: "white" }} color={"#CF3A3A"}>
              Não Inicializada
            </Tag>
          );
        } else if (item.status === "revisão técnica") {
          return <Tag color="error">Revisão técnica</Tag>;
        } else if (item.status === "revisada") {
          return <Tag color="success">Revisada</Tag>;
        } else {
          return (
            <Tag style={{ color: "white" }} color={"black"}>
              Outro
            </Tag>
          );
        }
      },
    },
    {
      title: "Informações",
      dataIndex: "info",
      key: "info",
      align: "center",
      render: (id, item) => {
        return (
          <ProposalInfo state={allProposals.filter((i) => i.id === item.id)} />
        );
      },
    },
    {
      title: "Template",
      dataIndex: "id",
      key: "template",
      align: "center",
      render: (id, item) => {
        return (
          <NavLink to={`/technical-proposals/template/${id}`} state={item}>
            <Button
              type="text"
              onClick={() => {
                const username = localStorage.getItem("@dsm/username");
                const title = "Criação de Template";
                const description = `${username} criou o template a partir da proposta ${item.name}`;
                logNewAuditAction(username, title, description);
              }}
            >
              {loading === true ? <LoadingOutlined /> : <FileOutlined />}
            </Button>
          </NavLink>
        );
      },
    },
    {
      title: "Editar",
      dataIndex: "id",
      key: "edit",
      align: "center",
      render: (id, item) => {
        return (
          <Button
            type="text"
            onClick={() => handleGoTo(`/technical-proposals/edit/${id}`, item)}
            loading={loadingButtons}
          >
            {loading === true ? (
              <LoadingOutlined />
            ) : (
              <EditOutlined item={item} id={id} />
            )}
          </Button>
        );
      },
    },

    {
      title: "Ações",
      dataIndex: "actions",
      key: "actions",
      align: "center",
      render: (id, item) => {
        return (
          <Popconfirm
            okText="Sim"
            cancelText="Não"
            title={
              item.active === true
                ? "Deseja desativar essa proposta?"
                : "Deseja ativar essa proposta?"
            }
            placement="bottom"
            onConfirm={() => {
              setLoading(true);
              try {
                dynamoPut(
                  `${process.env.REACT_APP_STAGE}-proposals`,
                  item.id,

                  {
                    active: !item.active,
                  },
                  setLoading(true)
                ).then(() => {
                  dynamoGet(`${process.env.REACT_APP_STAGE}-proposals`).then(
                    (data) => {
                      const prefixTitle =
                        item.active === true ? "Desativação " : "Ativação ";
                      const prefixDescription =
                        item.active === true ? "desativou" : "ativou";
                      const username = localStorage.getItem("@dsm/username");
                      const title = prefixTitle + "de Proposta Técnica";
                      const description = `${username} ${prefixDescription} a proposta: ${item.name}`;
                      logNewAuditAction(username, title, description);
                      setAllProposals(data);
                      setLoading(false);
                      message.success(
                        item.active
                          ? "Proposta desativada com sucesso"
                          : "Proposta ativada com sucesso"
                      );
                    }
                  );
                });
              } catch (err) {
                setLoading(false);
                message.error("Ocorreu um erro ao atualizar a proposta!");
              }
            }}
          >
            <Button danger type="text">
              <Tag color={item.active === true ? "red" : "green"}>
                {item.active === true ? "Desativar" : "Ativar"}
              </Tag>
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const tableData = useMemo(() => {
    let filteredTableData = filterTableData({
      searchFields: ["name", "customer"],
      search,
      data: allProposals,
    });

    // Garantir que filteredTableData seja sempre um array
    if (!Array.isArray(filteredTableData)) {
      filteredTableData = [];
    }

    filteredTableData = filteredTableData.filter((e) => {
      switch (actionsState) {
        case "todas":
          return e;
        case "não inicializada":
          return e?.status.toLocaleLowerCase() === "não inicializada";
        case "em andamento":
          return e?.status === "em andamento";
        case "concluída":
          return e?.status === "concluída";
        case "revisão técnica":
          return e?.status === "revisão técnica";
        default:
          return e;
      }
    });
    return filteredTableData;
  }, [allProposals, search, actionsState]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row
              justify="space-between"
              style={{ marginBottom: "1rem" }}
              gutter={[8, 8]}
            >
              <Col>
                <Space wrap>
                  <Input
                    style={{
                      width: "300px",
                      height: "35px",
                      borderRadius: "7px",
                    }}
                    placeholder="Buscar por Nome, Cliente, Arquiteto, Status..."
                    onChange={(e) => setSearch(e.target.value)}
                  />
                  <Button
                    type="primary"
                    onClick={() => handleGoTo(`/technical-proposals/add`)}
                    loading={loadingButtons}
                  >
                    Cadastrar Proposta <PlusOutlined />
                  </Button>
                </Space>
              </Col>
              <Col>
                <Space>
                  <Text>Filtrar por: </Text>
                  <Select
                    onChange={setActionsState}
                    defaultValue="todas"
                    style={{ width: "10rem" }}
                  >
                    <Option value="todas">Todas</Option>
                    <Option value="não inicializada">Não Inicializadas</Option>
                    <Option value="em andamento">Em Andamento</Option>
                    <Option value="concluída">Concluídas</Option>
                    <Option value="revisão técnica">Revisão Técnica</Option>
                  </Select>
                </Space>
              </Col>
            </Row>

            <AlertProposalInProgress />
            <Counter tableData={tableData} />
            <Table
              loading={loading}
              scroll={{ x: "100%" }}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "30", "50", "100"],
              }}
              dataSource={tableData}
              columns={columns}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
