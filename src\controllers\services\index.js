import { v4 } from "uuid";
import { message } from "antd";
import { dynamoGet, dynamoPost, dynamoPut } from "../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { dynamoGenericDelete } from "../../service/apiDynamoGeneric";
import { setServicesState } from "../../store/actions/services-action";
export const handleAddSubActivity = (activityKey, setActivities) => {
  setActivities((prevState) => prevState.map((item) => {
      if (item.id === activityKey) {
        return {
          ...item,
          subtasks: [...item.subtasks, { index: v4(), value: "" }],
        };
      }
      return item;
    }));
};

export const handleRemoveSubActivity = (
  activityKey,
  id,
  checkOrigin,
  setActivities
) => {
  setActivities((prevState) => prevState.map((item) => {
      if (item.id === activityKey) {
        return {
          ...item,
          subtasks: item.subtasks.filter((item) => item.index !== id),
        };
      }
      return item;
    }));
  if (checkOrigin === true) {
    message.warning("Sub Atividades vazias removidas!");
  } else {
    message.warning("Sub Atividade removida com sucesso!");
  }
};

export const handleSubActivityChange = (
  value,
  id,
  activityKey,
  setActivities
) => {
  setActivities((prevState) =>
    prevState.map((item) => {
      if (item.id === activityKey) {
        return {
          ...item,
          subtasks: item.subtasks.map((item) => {
            if (item.index === id) {
              return {
                ...item,
                value: value,
              };
            }

            return item;
          }),
        };
      }

      return item;
    })
  );
};

export const handleAddActivity = (setActivities) => {
  setActivities((prev) => [
    ...prev,
    {
      id: v4(),
      subtasks: [{ index: v4(), value: "" }],
      name: "",
      estimates: {
        "8x5setup": 0,
        "24x7setup": 0,
        "8x5sust": 0,
        "24x7sust": 0,
        "8x5dbasec": 0,
        "24x7dbasec": 0,
      },
    },
  ]);
};

export const handleRemoveActivity = (index, setActivities) => {
  setActivities((prev) => prev.filter(({ id }) => index !== id));
  message.warning("Atividade removida com sucesso!");
};

export const handleActivityChange = (value, id, field, setActivities) => {
  setActivities((prev) => prev.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          estimates: {
            ...item.estimates,
            [field]: value,
          },
          [field]: value,
        };
      }
      return item;
    }));
};

export const handleNullSubtask = (activities) => {
  activities.map((item) => {
    return item.subtasks.map((subItem, subItemKey) => {
      if (subItem.value === "" && item.subtasks.length > 1) {
        handleRemoveSubActivity(item.id, subItem.index, true);
      }
    });
  });
};

export const handleCheckServiceName = (
  inputValue,
  allServices,
  setExistingName
) => {
  if (
    allServices.find(
      (service) => service?.name?.toLowerCase() === inputValue?.toLowerCase()
    ) !== undefined
  ) {
    setExistingName(true);
  } else {
    setExistingName(false);
  }
};

export const handleSubmit = async (
  data,
  serviceName,
  serviceDescription,
  activities,
  tagName,
  existingDescription = false,
  setLoading,
  mainTagColorInput,
  navigate,
  existingName,
  type = "create",
  item = null
) => {
  setLoading(true);

  if (!serviceDescription || serviceDescription === "") {
    message.warning("Preencha a descrição do serviço.");
  } else {
    data["tag_name"] = tagName;
    handleNullSubtask(activities);

    try {
      if (existingName === true) {
        message.error("Já existe um serviço com esse nome!");
      } else if (existingDescription === true) {
        message.error("Já existe um serviço com essa descrição!");
      } else {
        const formattedBody = {
          id: v4(),
          name: serviceName,
          description: serviceDescription,
          active: true,
          management: false,
          tag: {
            name: tagName,
            rgb: data?.tag_color ? data.tag_color : mainTagColorInput,
          },
          tasks: activities.map((item) => ({
            name: item.name,
            description: item.description,
            estimates: {
              "8x5dbasec": Number(item.estimates["8x5dbasec"]),
              "8x5setup": Number(item.estimates["8x5setup"]),
              "8x5sust": Number(item.estimates["8x5sust"]),
              "24x7dbasec": Number(item.estimates["24x7dbasec"]),
              "24x7setup": Number(item.estimates["24x7setup"]),
              "24x7sust": Number(item.estimates["24x7sust"]),
            },
            subtasks: item.subtasks
              .map((currentSubtask, id) => {
                if (id === 0) {
                  if (currentSubtask.value !== "") {
                    return { description: currentSubtask.value };
                  } else if (
                    item?.subtasks?.length > 1 &&
                    currentSubtask.value === ""
                  ) {
                    return { description: "" };
                  } else {
                    return {
                      description: "Atividade sem Sub Atividades definidas",
                    };
                  }
                } else {
                  return { description: currentSubtask.value };
                }
              })
              .filter((item) => item.description !== "")
              .map((item) =>
                item.description === "Atividade sem Sub Atividades definidas"
                  ? { description: "" }
                  : { description: item.description }
              ),
          })),
        };
        if (type === "edit") {
          await dynamoPut(
            `${process.env.REACT_APP_STAGE}-services`,
            item.id,
            formattedBody
          );
        } else {
          await dynamoPost(
            `${process.env.REACT_APP_STAGE}-services`,
            formattedBody
          );
        }
        const username = localStorage.getItem("@dsm/username");
        const title = "Adição de Serviço";
        const description = `${username} adicionou o serviço: ${serviceName}`;
        logNewAuditAction(username, title, description);
        message.success("Serviço criado com sucesso!");
        navigate("/services");
      }
    } catch (error) {
      message.error("Erro ao adicionar serviço");
    }
  }
  setLoading(false);
};

export const handleGetServices = async (setLoading) => {
  setLoading(true);
  try {
    const data = await dynamoGet(`${process.env.REACT_APP_STAGE}-services`);
    const servicesArray = Array.isArray(data) ? data : [];
    setServicesState(servicesArray);
    setLoading(false);
  } catch (error) {
    console.error('Erro ao buscar services:', error);
    setServicesState([]);
    setLoading(false);
  }
};

export const handleToggleService = async (setLoading, item) => {
  setLoading(true);
  try {
    await dynamoPut(
      `${process.env.REACT_APP_STAGE}-services`,
      item.id,
      {
        active: item.active === true ? false : true,
      },
      setLoading(true)
    );
    const prefixTitle = item.active === true ? "Desativação " : "Ativação ";
    const prefixDescription = item.active === true ? "desativou" : "ativou";
    const username = localStorage.getItem("@dsm/username");
    const title = prefixTitle + "de Serviço";
    const description = `${username} ${prefixDescription} o serviço: ${item.name}`;
    logNewAuditAction(username, title, description);

    const data = await dynamoGet(`${process.env.REACT_APP_STAGE}-services`);

    setServicesState(data);
    setLoading(false);
    message.success(
      `Serviço ${item.active === true ? "desativado" : "ativado"} com sucesso!`
    );
  } catch (error) {
    setLoading(false);
    message.error("Ocorreu um erro ao atualizar o serviço!");
  }
};
