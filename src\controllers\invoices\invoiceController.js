import { message } from "antd";

import { dsm<PERSON>rovider } from "../../provider/dsm-provider";
import { setInvoiceFieldState } from "../../store/actions/invoice-action";

import { dolarGet, dynamoGetById } from "../../service/apiDsmDynamo";
import moment from "moment";
import dayjs from "dayjs";

export async function getInvoices({ month, year, dolar, customers }) {
  try {
    const provider = dsmProvider();
    const { data } = await provider.get(
      `/read-invoices?fullDate=${year}-${month}`
    );

    if (data.data.Items.length === 0)
      message.warning("Não há invoices para serem exibidos nesta data", 10);

    const {
      invoices,
      total_neg,
      total_pos,
      total_refund,
      total_edp,
      total_spp,
    } = formactInvoices({
      invoiceList: data.data.Items,
      dolar,
      customers,
    });

    setInvoiceFieldState({ field: "invoices", value: invoices });
    setInvoiceFieldState({
      field: "totalCash",
      value: { total_pos, total_neg, total_refund, total_edp, total_spp },
    });

    return invoices;
  } catch (error) {
    console.log(error);
    setInvoiceFieldState({ field: "invoices", value: [] });
  }
}

export async function getPermission() {
  try {
    let data = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-permissions`,
      localStorage.getItem("@dsm/permission")
    );

    // ✅ Verificação robusta da estrutura de dados
    if (data?.permissions) {
      const invoicePage = data.permissions.find((x) => x.page === "Invoices");
      if (invoicePage?.actions) {
        const permissions = [...invoicePage.actions];
        setInvoiceFieldState({ field: "permissions", value: permissions });
        return;
      }
    }

    throw new Error('Invalid permissions structure');
  } catch (error) {
    const fallbackPermissions = [
      { code: "view_bill_payer_account" },
      { code: "view_account_id" },
      { code: "view_customer" },
      { code: "view_billing_entity" },
      { code: "view_invoice_id" },
      { code: "view_month" },
      { code: "view_cost" },
      { code: "view_spp_discount" },
      { code: "view_edp_discount" },
      { code: "view_bundled_discount" },
      { code: "view_saving_plans_discount" },
      { code: "view_credits" },
      { code: "view_other_discounts" },
      { code: "view_conversion" },
      { code: "view_total_discount" },
      { code: "view_line_item_legal_entity" },
      { code: "view_final_balance" },
      { code: "upload_invoice" }
    ];
    setInvoiceFieldState({ field: "permissions", value: fallbackPermissions });
  }
}

export async function getDolarValue({ month, year }) {
  let dolar = 0;
  try {
    dolar = await dolarGet(month, year);
    dolar = typeof dolar === "number" ? dolar : dolar.value;
  } catch (error) {
    console.log(error);
  }

  if (!dolar) {
    message.warning(
      "Cotação do dólar ainda não cadastrada para este mês, por favor, realize o upload de algum invoice referente ao mês atual",
      10
    );
  }

  setInvoiceFieldState({ field: "dolar", value: dolar });
  return dolar;
}

export function formactInvoices({ invoiceList, dolar, customers }) {
  let invoices = [];
  let total_pos = 0;
  let total_neg = 0;
  let total_refund = 0;
  let total_edp = 0;
  let total_spp = 0;

  for (const invoice of invoiceList) {
    let accounts = invoice.accounts;
    for (const account of accounts) {
      let tax = 0;
      let charges = 0;
      if (account.cost > 0) total_pos += account.cost;
      else total_neg += account.cost;
      if (account?.line_item_type?.includes("Refund")) {
        total_refund += account.cost;
      }

      if (account?.line_item_type?.includes("EdpDiscount")) {
        total_edp += parseFloat(account.cost || 0);
      }

      if (account?.line_item_type?.includes("SppDiscount"))
        total_spp += parseFloat(account.cost || 0);

      if (account?.line_item_type?.includes("Tax")) {
        tax += parseFloat(account.cost || 0);
      }

      if (account.cost > 0) {
        charges += parseFloat(account.cost || 0);
        charges -= tax;
      }

      if (account?.line_item_type?.includes("Refund") && account.cost < 0) {
        charges += parseFloat(account.cost || 0);
      }

      const customer = customers.find(
        (c) =>
          Array.isArray(c.accounts) &&
          c.accounts.some(
            (a) =>
              a.account_id === account.sub_account ||
              a.account_id === account.payer_account_id
          )
      );

      account["customer"] = "";
      if (customer)
        account["customer"] =
          customer.names.name || customer.names.fantasy_name;

      account["conversion"] =
        parseFloat(parseFloat(dolar || 0) * parseFloat(account.cost)).toFixed(
          2
        ) || 0;

      const spp_discount = parseFloat(Math.abs(account.spp_discount || 0));
      const total_edp_discount = parseFloat(
        Math.abs(account.edp_discount || 0)
      );
      const tax_percentage = calculateTaxPercentage(
        tax,
        charges,
        Math.abs(account.total_discount || 0)
      );
      const final_balance =
        account.cost + spp_discount + Math.abs(total_edp_discount);

      invoices.push({
        line_item_usage_account_id: account.sub_account,
        unblended_cost: account.cost,
        month: invoice?.month,
        year: invoice?.year,
        bill_invoice_id: account.invoice_id
          ? account.invoice_id
          : invoice?.invoice_id,
        bill_payer_account_id: account.payer_account_id
          ? account.payer_account_id
          : invoice?.payer_account,
        bill_billing_entity: account.bill_entity,
        line_item_legal_entity: account.legal_entity,
        customer: account["customer"],
        conversion: account["conversion"],
        discount: Math.abs(account.discount || 0),
        spp_discount: -spp_discount.toFixed(2),
        total_discount: -parseFloat(
          Math.abs(account.total_discount || 0)
        ).toFixed(2),
        total_edp_discount: -total_edp_discount.toFixed(2),
        total_credits: -parseFloat(Math.abs(account.credit || 0)).toFixed(2),
        total_bundled_discount: -parseFloat(
          Math.abs(account.bundled_discount || 0)
        ).toFixed(2),
        total_saving_plans_discount: -parseFloat(
          Math.abs(account.savingPlans || 0)
        ).toFixed(2),
        final_balance: final_balance,
        line_item_type: account.line_item_type,
        tax: tax,
        charges: charges,
        tax_percentage: tax_percentage,
        other_discounts: -parseFloat(
          Math.abs(account?.other_discounts || 0)
        ).toFixed(2),
      });
    }

    if (invoices.length > 0) {
      invoices[0]["dolar"] = `$ ${parseFloat(dolar || "0").toFixed(2)}`;
    }
  }

  return {
    invoices,
    total_pos,
    total_neg,
    total_refund,
    total_edp,
    total_spp,
  };
}

export const filterByState = (data, state) => {
  if (!data) return [];

  switch (state) {
    case 'saldoPos':
      return data.filter(item => item.unblended_cost > 0);
    case 'saldoNeg':
      return data.filter(item => item.unblended_cost < 0);
    case 'todos':
    default:
      return data;
  }
};

export const filterBySearch = (data, search) => {
  let filteredData = [];
  if (data) {
    filteredData = data.filter((e) => {
      let verifyCustomer,
        verifyAccountId,
        verifyBillPayerAccountId,
        verifyUnblendedCost,
        verifyMonth,
        verifyBillId,
        verifyBillBillingEntity,
        verifyLegalEntity,
        verifyConversion,
        verifyTypes = false;
      if (e?.customer) {
        verifyCustomer = e?.customer
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.line_item_usage_account_id) {
        verifyAccountId = e?.line_item_usage_account_id
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.bill_payer_account_id) {
        verifyBillPayerAccountId = e?.bill_payer_account_id
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.unblended_cost) {
        verifyUnblendedCost = e?.unblended_cost
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.month) {
        verifyMonth = e?.month
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.bill_invoice_id) {
        verifyBillId = e?.bill_invoice_id
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.bill_billing_entity) {
        verifyBillBillingEntity = e?.bill_billing_entity
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.line_item_legal_entity) {
        verifyLegalEntity = e?.line_item_legal_entity
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.conversion) {
        verifyConversion = e?.conversion
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (e.line_item_type) {
        verifyTypes = e?.line_item_type
          ?.toString()
          ?.toLowerCase()
          ?.includes(search?.toLowerCase());
      }

      if (
        verifyCustomer ||
        verifyAccountId ||
        verifyBillPayerAccountId ||
        verifyUnblendedCost ||
        verifyMonth ||
        verifyBillId ||
        verifyBillBillingEntity ||
        verifyLegalEntity ||
        verifyConversion ||
        verifyTypes
      ) {
        return e;
      } else {
        return false;
      }
    });
  }

  return filteredData;
};

export function groupInvoiceByAccount(invoices, dolar) {
  const invoiceList = invoices.reduce((list, invoice) => {
    let newList = JSON.stringify(list);
    newList = JSON.parse(newList);

    let invoiceIndex = newList.findIndex(
      (i) =>
        i.line_item_usage_account_id === invoice.line_item_usage_account_id &&
        i.bill_invoice_id === invoice.bill_invoice_id
    );
    if (invoiceIndex < 0) {
      newList.push(invoice);
    } else {
      calculateInvoiceTotals(newList, invoiceIndex, invoice, dolar);
    }

    return newList;
  }, []);

  return formatInvoiceValues(invoiceList, dolar);
}

export function groupInvoiceByInvoiceId(invoices, dolar) {
  const invoiceList = invoices.reduce((list, invoice) => {
    let newList = JSON.stringify(list);
    newList = JSON.parse(newList);

    let invoiceIndex = newList.findIndex(
      (i) => i.bill_invoice_id === invoice.bill_invoice_id
    );
    if (invoiceIndex < 0) {
      newList.push(invoice);
    } else {
      calculateInvoiceTotals(newList, invoiceIndex, invoice, dolar);
    }

    return newList;
  }, []);
  return formatInvoiceValues(invoiceList, dolar);
}

export function calculateDiscounts(existingDiscount, newDiscount) {
  return parseFloat(existingDiscount) + parseFloat(newDiscount);
}

export function calculateTotals(existingTotal, newAmount) {
  return parseFloat(existingTotal) + parseFloat(newAmount);
}

export function calculateTaxPercentage(tax, charges, discountsTotal) {
  if (tax === 0) {
    return "0.00%";
  }
  return (
    (
      (parseFloat(tax) / (parseFloat(charges) + parseFloat(discountsTotal))) *
      100
    ).toFixed(2) + "%"
  );
}

export function calculateInvoiceTotals(newList, invoiceIndex, invoice, dolar) {
  const spp_discount = calculateDiscounts(
    newList[invoiceIndex].spp_discount,
    invoice.spp_discount
  );
  const total_credits = calculateTotals(
    newList[invoiceIndex].total_credits,
    invoice.total_credits
  );
  const total_discount = calculateDiscounts(
    newList[invoiceIndex].total_discount,
    invoice.total_discount
  );
  const total_edp_discount = calculateDiscounts(
    newList[invoiceIndex].total_edp_discount,
    invoice.total_edp_discount
  );
  const total_bundled_discount = calculateDiscounts(
    newList[invoiceIndex].total_bundled_discount,
    invoice.total_bundled_discount
  );
  const total_saving_plans_discount = calculateDiscounts(
    newList[invoiceIndex].total_saving_plans_discount,
    invoice.total_saving_plans_discount
  );

  const final_balance = calculateTotals(
    newList[invoiceIndex].final_balance,
    invoice.final_balance
  );
  const unblended_cost = calculateTotals(
    newList[invoiceIndex].unblended_cost,
    invoice.unblended_cost
  );
  const conversion =
    parseFloat(parseFloat(dolar || 0) * parseFloat(unblended_cost)) || 0;
  const tax = calculateTotals(newList[invoiceIndex].tax, invoice.tax);

  const other_discounts = calculateTotals(
    newList[invoiceIndex].other_discounts,
    invoice.other_discounts || 0
  );

  const charges = calculateTotals(
    newList[invoiceIndex].charges,
    invoice.charges
  );

  const tax_percentage = calculateTaxPercentage(
    tax,
    charges,
    spp_discount +
      total_edp_discount +
      total_credits +
      total_saving_plans_discount +
      total_bundled_discount +
      other_discounts
  );

  newList[invoiceIndex].spp_discount = spp_discount;
  newList[invoiceIndex].total_credits = total_credits;
  newList[invoiceIndex].total_discount = total_discount;
  newList[invoiceIndex].total_edp_discount = total_edp_discount;
  newList[invoiceIndex].total_bundled_discount = total_bundled_discount;
  newList[invoiceIndex].total_saving_plans_discount =
    total_saving_plans_discount;
  newList[invoiceIndex].final_balance = final_balance;
  newList[invoiceIndex].unblended_cost = unblended_cost;
  newList[invoiceIndex].conversion = conversion;
  newList[invoiceIndex].tax = tax;
  newList[invoiceIndex].other_discounts = other_discounts;
  newList[invoiceIndex].charges = parseFloat(charges).toFixed(2);
  newList[invoiceIndex].tax_percentage = tax_percentage || "0.00%";
}

function formatInvoiceValues(invoiceList, dolar) {
  return invoiceList.map((invoice) => {
    return {
      ...invoice,
      spp_discount: parseFloat(invoice.spp_discount).toFixed(2),
      total_credits: parseFloat(invoice.total_credits).toFixed(2),
      total_discount: parseFloat(invoice.total_discount).toFixed(2),
      unblended_cost: parseFloat(invoice.unblended_cost).toFixed(2),
      total_edp_discount: parseFloat(invoice.total_edp_discount).toFixed(2),
      total_bundled_discount: parseFloat(
        invoice.total_bundled_discount
      ).toFixed(2),
      total_saving_plans_discount: parseFloat(
        invoice.total_saving_plans_discount
      ).toFixed(2),
      conversion: parseFloat(invoice.conversion || 0).toFixed(2),
      final_balance: parseFloat(invoice.final_balance).toFixed(2),
      final_balance_real: (
        parseFloat(invoice.final_balance) * parseFloat(dolar || 0)
      ).toFixed(2),
      tax: parseFloat(invoice.tax).toFixed(2),
      other_discounts: parseFloat(invoice.other_discounts).toFixed(2),
    };
  });
}

export function formatDateRange(dateRange) {
  if (!dateRange) return null;
  try {
    // Retornar objeto dayjs ao invés de moment para compatibilidade com Ant Design 5.x
    return dayjs(`${dateRange.year}-${String(dateRange.month).padStart(2, '0')}-01`);
  } catch (error) {
    console.error('⚠️ Invoices: Erro ao formatar dateRange:', error);
    return null;
  }
}
