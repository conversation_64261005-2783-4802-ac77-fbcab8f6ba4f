import { Mo<PERSON>, But<PERSON>, Tag, Typography, Form, Row, message } from "antd";

import { Select } from "../../Select";
import { useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import { dynamoPut, dynamoGetById } from "../../../service/apiDsmDynamo";
import { changeContractStatus } from "../../../controllers/contracts/contract-controller";
import { dsmGet } from "../../../service/apiDsm";
import { shallowEqual, useSelector } from "react-redux";
import { setContractFieldState } from "../../../store/actions/contract-action";

export const InactiveContractReason = ({
  contract,
  setLoading,
  controller,
  getCurrentCustomerContracts,
}) => {
  const { Option } = Select;
  const [showModal, setShowModal] = useState(false);
  const [inactivateLoading, setInactivateLoading] = useState(false);
  const [currentContractInfo, setCurrentContractInfo] = useState(undefined);
  const { Text } = Typography;
  const { id, name, active } = contract;
  const [form] = Form.useForm();
  const { deactivateReasonsValues } = useSelector(
    (state) => state.contract,
    shallowEqual
  );

  const handleOpenModal = async () => {
    // 🔍 Debug: Verificar dados do contrato
    console.log('🔍 InactiveContractReason - handleOpenModal:', {
      contractId: id,
      contractName: name,
      contractActive: active,
      expectedAction: active === 1 ? 'Desativar' : 'Ativar'
    });

    setShowModal(true);
    const currentContractInfo = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-contracts`,
      id
    );
    setCurrentContractInfo(currentContractInfo);
    if (deactivateReasonsValues?.length === 0)
      await getDeactivateReasonsValues();
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    setInactivateLoading(true);
    try {
      await changeContractStatus(id, values, (status) => {
        setInactivateLoading(status);
        setShowModal(status);
        setLoading(status);
      });

      // Aguardar um pequeno delay para evitar conflitos de estado
      setTimeout(async () => {
        if (getCurrentCustomerContracts) {
          await getCurrentCustomerContracts();
        }
      }, 500);

    } catch (error) {
      setLoading(false);
      setInactivateLoading(false);
      setShowModal(false);
      message.error(
        `Erro ao ${
          active === 1 ? "desativar" : "ativar"
        } contrato, tente novamente mais tarde!`
      );
    }
  };

  async function getDeactivateReasonsValues() {
    const response = await dsmGet(`contracts/reasons-to-deactivate`);
    setContractFieldState({
      field: "deactivateReasonsValues",
      value: response,
    });
  }

  return (
    <>
      <>
        <Tag
          color={active === 1 ? "red" : "green"}
          style={{ cursor: "pointer" }}
          onClick={handleOpenModal}
        >
          {active === 1 ? "Desativar" : "Ativar"}
        </Tag>
        <Modal
          title={`${active === 1 ? "Desativar" : "Ativar"} contrato`}
          open={showModal}
          onCancel={() => {
            setShowModal(false);
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                setShowModal(false);
              }}
            >
              Cancelar
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={inactivateLoading}
              onClick={() => {
                form.submit();
              }}
            >
              {active === 1 ? "Desativar" : "Ativar"}
            </Button>,
          ]}
        >
          {active === 1 ? (
            <Text>Explique o motivo da desativação do contrato</Text>
          ) : (
            <Text>
              Tem certeza que deseja ativar este contrato?
              <br />
              {currentContractInfo?.reasonForDeactivation
                ? "Confira o motivo da desativação abaixo:"
                : ""}
            </Text>
          )}
          {!currentContractInfo ? (
            <Row justify="center" style={{ marginTop: "10px" }}>
              <LoadingOutlined />
            </Row>
          ) : (
            <Form
              style={{ marginTop: "10px" }}
              layout="vertical"
              name="inactive-contract-form"
              form={form}
              onFinish={handleSubmit}
            >
              <Form.Item
                name="reason"
                initialValue={
                  currentContractInfo.reasonForDeactivation
                    ? currentContractInfo.reasonForDeactivation
                    : null
                }
                rules={[
                  {
                    required: active === 1,
                    message:
                      "Por favor, informe o motivo da desativação do contrato",
                  },
                ]}
              >
                <Select
                  disabled={active === 0}
                  style={{ width: "100%" }}
                  placeholder="Motivo da desativação"
                  options={deactivateReasonsValues}
                  loading={deactivateReasonsValues.length === 0}
                ></Select>
              </Form.Item>
            </Form>
          )}
        </Modal>
      </>
    </>
  );
};
