import React, { useEffect, useState } from "react";
import { Row, Col, Typography, Select, Form, Button, Alert } from "antd";
import { useSelector, shallowEqual } from "react-redux";

import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";

import axios from "axios";

const { Title } = Typography;
const { Option } = Select;

export const ArchitectsField = () => {
    const architects = useSelector(state => state.technicalProposal.architects, shallowEqual)

    const [architectList, setArchitectList] = useState([]);
    const [loading, setLoading] = useState([])
    const [usingMockData, setUsingMockData] = useState(false)

    useEffect(() => {
        getUsers()
    }, [])

    // Mock data for development when API fails
    const getMockArchitects = () => {
        console.log('🎯 ArchitectsField: Usando dados mockados para desenvolvimento');
        return [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
    };

    async function getUsers(retryCount = 0) {
        try {
            setLoading(true)

            const { data } = await axios.get(
                `${process.env.REACT_APP_API_PERMISSION}cognito/read`,
                {
                    headers: {
                        Authorization: localStorage.getItem("jwt"),
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000 // 10 seconds timeout
                }
            );

            let users = [];
            if (data?.data && Array.isArray(data.data)) {
                users = data.data.map(user => user.email).filter(email => email);
            } else if (data?.Items && Array.isArray(data.Items)) {
                users = data.Items.map(user => user.email).filter(email => email);
            } else if (Array.isArray(data)) {
                users = data.map(user => user.email).filter(email => email);
            } else {
                users = [];
            }

            if (users.length === 0) {
                users = getMockArchitects();
                setUsingMockData(true);
            } else {
                setUsingMockData(false);
            }

            setArchitectList(users);
            setLoading(false)
        } catch (error) {
            if (retryCount < 2 && (!error.response || error.response.status >= 502)) {
                setTimeout(() => getUsers(retryCount + 1), 2000);
                return;
            }

            if (process.env.NODE_ENV === 'development' || error.response?.status === 500) {
                setArchitectList(getMockArchitects());
                setUsingMockData(true);
            } else {
                setArchitectList([]);
                setUsingMockData(false);
            }

            setLoading(false)
        }
    }

    return (
        <Col sm={24} md={12} lg={6}>
            <Row>
                <Title level={5} style={{ fontWeight: "400" }}>
                    Arquitetos
                </Title>
            </Row>
            {usingMockData && (
                <Row style={{ marginBottom: 8 }}>
                    <Col span={24}>
                        <Alert
                            message="Usando dados de desenvolvimento"
                            description="A API de usuários não está disponível. Usando lista mockada para desenvolvimento."
                            type="warning"
                            size="small"
                            showIcon
                            closable
                        />
                    </Col>
                </Row>
            )}
            <Row>
                <Col span={20}>
                        <Select
                            id="architects"
                            loading={loading}
                            mode="multiple"
                            value={architects}
                            showSearch
                            placeholder={"Nome do arquiteto"}
                            optionFilterProp="children"
                            filterOption={(input, option) =>
                                option?.children
                                    ?.toLowerCase()
                                    .includes(input?.toLowerCase())
                            }
                            filterSort={(optionA, optionB) =>
                                optionA?.children
                                    ?.toLowerCase()
                                    .localeCompare(optionB.children?.toLowerCase())
                            }
                            onChange={(e) => setTechnicalProposalState({ field: 'architects', value: e })}
                            style={{ width: '100%' }}
                        >

                            {
                                architectList.map((t, index) =>
                                    <Option key={index} value={t}>{t}</Option>
                                )
                            }

                        </Select>
                </Col>
            </Row>
        </Col>
    )
}