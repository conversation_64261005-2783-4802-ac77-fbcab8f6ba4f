import { EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useState } from "react";
import { Button, message, Modal, Popconfirm, Table, Tag } from "antd";
// import v4 from "uuid/v4";
import { dynamoPut } from "../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

export const EditFunction = ({ permission, item, mutate }) => {
  const [showModal, setShowModal] = useState();
  const [loading, setLoading] = useState(false);

  const handleDelete = async (props) => {
    // Validar se os dados necessários estão disponíveis
    if (!permission || !permission.id || !permission.all) {
      // console.error('❌ EditFunction: Dados inválidos para exclusão:', {
      //   hasPermission: !!permission,
      //   hasId: !!permission?.id,
      //   hasAll: !!permission?.all,
      //   permissionId: permission?.id
      // });
      message.error("Erro: Dados não carregados. Recarregue a página e tente novamente.");
      setLoading(false);
      return;
    }

    const code = props.code;
    setLoading(true);

    try {
      // Criar o novo objeto preservando o ID e outras propriedades
      const updatedPermission = {
        ...permission,
        all: permission.all.map((p) => {
          if (p.page === item.page) {
            return {
              ...p,
              permissions: p.permissions.filter((perm) => perm.code !== code),
            };
          }
          return p;
        }),
      };

      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-page-actions`,
        permission.id,
        updatedPermission
      );

      // Atualizar o estado local preservando todas as propriedades
      mutate(updatedPermission, false);

      const username = localStorage.getItem("@dsm/username");
      const title = "Remoção de Funcionalidades";
      const description = `${username} removeu a funcionalidade ${props.action} da página ${item.page}`;
      logNewAuditAction(username, title, description);

      message.success("Função removida da página com sucesso!");
    } catch (error) {
      console.error('❌ EditFunction: Erro ao deletar funcionalidade:', error);
      message.error("Erro ao tentar remover funcionalidade...");
    }

    setLoading(false);
  };

  const columns = [
    {
      title: "Ações na página",
      dataIndex: "action",
      key: "action",
    },
    {
      title: "Código",
      dataIndex: "code",
      key: "code",
      render: (id, props) => {
        return (
          <Tag color="green" key={id}>
            {props.code}
          </Tag>
        );
      },
    },
    {
      title: "Remover",
      dataIndex: "delete",
      key: "delete",
      width: "1%",
      align: "center",
      render: (id, props) => {
        return (
          <Popconfirm
            title="Tem certeza de que quer remover essa ação?"
            onConfirm={() => {
              handleDelete(props);
            }}
            onCancel={() => {}}
            okText="Sim"
            cancelText="Não"
          >
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        );
      },
    },
  ];

  const handleModalOpen = () => {
    setShowModal(true);
  };

  return (
    <>
      <Button
        icon={<EditOutlined />}
        type="text"
        onClick={handleModalOpen}
      ></Button>
      <Modal
        title="Visualização de funções"
        open={showModal}
        closable={false}
        onOk={() => setShowModal(false)}
        okText="Ok"
        cancelText="Cancelar"
        onCancel={() => setShowModal(false)}
      >
        {
          <Table
            pagination={true}
            loading={loading}
            dataSource={item?.permissions}
            columns={columns}
            scroll={{ x: 100 }}
          ></Table>
        }
      </Modal>
    </>
  );
};
