import {
  Modal,
  Button,
  Input,
  Select,
  DatePicker,
  Form,
  message,
  Typography,
  InputNumber,
  
} from "antd";
import React, { useState } from "react";
import { format, differenceInCalendarMonths } from "date-fns";
import {
  createContract,
  dynamoPost,
  dynamoPut,
  useDynamoGet,
} from "../../../service/apiDsmDynamo";
import { otrsPost, otrsGet } from "../../../service/apiOtrs";
import { withOtrsHealthCheck, shouldProceedWithOtrsOperation } from "../../../utils/otrsHealthCheck";
import { RequiredLabelForm } from "../../RequiredLabelForm";

import * as controller from "../../../controllers/contracts/contract-controller";
import { updateConsumptionHourContract } from "../../../provider/otrs-calculate-hours-provider";
import { formatCreateContractForm } from "./controllers/addContract";

export const AddContractModal = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loadingClients, setLoadingClients] = useState(false);
  const [loading, setLoading] = useState(false);
  const clients = useDynamoGet(`${process.env.REACT_APP_STAGE}-customers`);
  const { RangePicker } = DatePicker;
  const [form] = Form.useForm();
  const { Option } = Select;
  const contractValue = Form.useWatch('value', form);

  if (!clients.data && !loadingClients) {
    return setLoadingClients(true);
  }

  if (clients.data && loadingClients) {
    return setLoadingClients(false);
  }

  // Função para verificar se o contrato foi criado no OTRS
  const verifyContractInOtrs = async (contractId, maxAttempts = 5) => {
    console.log(`🔍 Verificando se contrato ${contractId} existe no OTRS...`);

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`🔄 Tentativa ${attempt}/${maxAttempts} de verificação...`);

        const response = await otrsGet(`read/contract/id/${contractId}`);

        if (response && response.data && response.data.id) {
          console.log('✅ Contrato confirmado no OTRS:', {
            contractId,
            otrsData: response.data,
            attempt
          });
          return true;
        }

        console.log(`⏳ Contrato ainda não encontrado no OTRS (tentativa ${attempt})`);

        // Aguardar antes da próxima tentativa (exceto na última)
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000 * attempt)); // 2s, 4s, 6s, 8s
        }

      } catch (error) {
        console.warn(`⚠️ Erro na verificação ${attempt}:`, error.message);

        // Se for erro 404, o contrato ainda não existe
        if (error.response?.status === 404) {
          if (attempt < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
            continue;
          }
        } else {
          // Para outros erros, não continuar tentando
          console.error('❌ Erro crítico na verificação:', error);
          break;
        }
      }
    }

    console.error(`❌ Contrato ${contractId} não foi encontrado no OTRS após ${maxAttempts} tentativas`);
    return false;
  };

  const handleSubmit = async (data) => {
    setLoading(true);

    try {
      // Função auxiliar para extrair data de forma robusta
      const extractDate = (dateValue) => {
        if (!dateValue) return null;

        try {
          // Se é um objeto dayjs/moment
          if (dateValue._d) {
            return new Date(dateValue._d);
          }

          // Se é um objeto dayjs com método toDate()
          if (typeof dateValue.toDate === 'function') {
            return dateValue.toDate();
          }

          // Se é um objeto moment com método toDate()
          if (typeof dateValue.toDate === 'function') {
            return dateValue.toDate();
          }

          // Se já é uma data ou string
          return new Date(dateValue);
        } catch (error) {
          console.error('❌ Erro ao extrair data:', error, dateValue);
          return null;
        }
      };

      const startDate = extractDate(data.expected_start_date[0]);
      const endDate = extractDate(data.expected_start_date[1]);

    console.log('🎯 Debug dates:', {
      rawStartDate: data.expected_start_date[0],
      rawEndDate: data.expected_start_date[1],
      extractedStartDate: startDate,
      extractedEndDate: endDate,
      startDateValid: startDate && !isNaN(startDate.getTime()),
      endDateValid: endDate && !isNaN(endDate.getTime())
    });

    if (!startDate || !endDate || isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.error('❌ Datas inválidas:', { startDate, endDate });
      message.error('Erro: Datas inválidas. Verifique as datas selecionadas.');
      setLoading(false);
      return;
    }

    const diff = differenceInCalendarMonths(endDate, startDate);

    // Validação e preparação do payload
    let customer;
    try {
      customer = JSON.parse(data.customer);
      if (!customer || !customer.itsm_id) {
        throw new Error('Cliente inválido ou sem ITSM ID');
      }
    } catch (parseError) {
      console.error('❌ Erro ao processar dados do cliente:', parseError);
      message.error('Dados do cliente inválidos. Selecione um cliente válido.');
      setLoading(false);
      return;
    }

    // Validar campos obrigatórios
    const totalHours = parseInt(data.total_hours);
    if (isNaN(totalHours) || totalHours <= 0) {
      console.error('❌ Total de horas inválido:', data.total_hours);
      message.error('Total de horas deve ser um número válido maior que zero.');
      setLoading(false);
      return;
    }

    if (!data.name || data.name.trim().length === 0) {
      console.error('❌ Nome do contrato inválido');
      message.error('Nome do contrato é obrigatório.');
      setLoading(false);
      return;
    }

    if (!data.type_hours) {
      console.error('❌ Tipo de horas não especificado');
      message.error('Tipo de horas é obrigatório.');
      setLoading(false);
      return;
    }

    const contractPayload = {
      name: data.name.trim(),
      customer_id: customer.itsm_id,
      start_date: format(startDate, "yyyy/MM/dd"),
      end_date: format(endDate, "yyyy/MM/dd"),
      duration: diff,
      total_hours: totalHours,
      type_hours: data.type_hours,
      document_id: null,
      sla_id: null,
      type_id: null,
      priority_id: null,
      notify_email: null,
      notify_percentage: null,
      freeze_percentage: null,
    };

    // Validação final do payload
    console.log('🔍 Validando payload do contrato:', contractPayload);
    if (!contractPayload.name || !contractPayload.customer_id || !contractPayload.total_hours) {
      console.error('❌ Payload inválido:', contractPayload);
      message.error('Dados do contrato incompletos. Verifique todos os campos obrigatórios.');
      setLoading(false);
      return;
    }

    console.log('🎯 OTRS Debug - Iniciando criação de contrato:', {
      baseUrl: process.env.REACT_APP_API_OTRS_BASE_URL,
      route: 'create/contract',
      fullUrl: process.env.REACT_APP_API_OTRS_BASE_URL + 'create/contract',
      payload: contractPayload,
      timestamp: new Date().toISOString()
    });

    try {
      // Verificar saúde da API OTRS antes de prosseguir
      console.log('🏥 Verificando saúde da API OTRS...');
      const healthCheck = await shouldProceedWithOtrsOperation();

      if (healthCheck.warning) {
        console.warn('⚠️ Aviso de saúde OTRS:', healthCheck.warning);
        message.warning(healthCheck.warning);
      }

      console.log('🔄 Enviando requisição para OTRS...');
      otrsPost("create/contract", contractPayload)
        .then(async (response) => {
          console.log('✅ Resposta recebida do OTRS:', {
            status: response?.status,
            hasData: !!response?.data,
            contractId: response?.data?.contract_id,
            fullResponse: response
          });

          try {
            // Validar resposta do OTRS
            if (!response || !response.data || !response.data.contract_id) {
              console.error('❌ Resposta inválida do OTRS:', response);
              throw new Error('OTRS não retornou um contract_id válido');
            }

            console.log('🔄 Formatando dados do contrato para DSM...');
            const contractData = formatCreateContractForm(data, diff, response);
            console.log('📋 Dados formatados:', contractData);

            console.log('🔄 Criando contrato no DSM...');
            await createContract(contractData);
            console.log('✅ Contrato criado no DSM com sucesso');

            // Verificar se o contrato foi realmente criado no OTRS
            console.log('🔍 Iniciando verificação de sincronização com OTRS...');
            const isContractInOtrs = await verifyContractInOtrs(response.data.contract_id);

            if (!isContractInOtrs) {
              console.warn('⚠️ Contrato não foi encontrado no OTRS após verificação');
              message.warning(
                `Contrato criado no DSM, mas pode não estar sincronizado com OTRS. ` +
                `ID OTRS: ${response.data.contract_id}. Verifique manualmente.`
              );
            } else {
              console.log('✅ Sincronização com OTRS confirmada');
            }

            if (response.data) {
              console.log('🔄 Atualizando consumo de horas no OTRS...');
              updateConsumptionHourContract({
                contractID: response.data.contract_id,
              });

              console.log('🔄 Atualizando status do cliente...');
              await dynamoPut(
                `${process.env.REACT_APP_STAGE}-customers`,
                JSON.parse(data.customer).id,
                {
                  active: 1,
                  has_active_contracts: 1,
                }
              );
              console.log('✅ Cliente atualizado com sucesso');
            }

            const username = localStorage.getItem("@dsm/username");

            const title = "Contrato criado";
            const description = `${username} criou o contrato ${data.name} (OTRS ID: ${response.data.contract_id}).`;

            console.log('🔄 Registrando auditoria...');
            dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
              username: username,
              name: title,
              description: description,
              created_at: new Date(),
              updated_at: new Date(),
            });

            controller.getActiveContracts();

            console.log('🎉 Processo de criação de contrato concluído com sucesso!');
            message.success(`Contrato criado com sucesso! ID OTRS: ${response.data.contract_id}`);

            setLoading(false);
            handleCancel();
          } catch (err) {
            console.error('❌ Erro no processamento pós-OTRS:', {
              error: err.message,
              stack: err.stack,
              contractData: data
            });
            setLoading(false);
            message.error(`Erro no processamento: ${err.message}`);
          }
        })
        .catch((error) => {
          console.error('❌ Erro na criação do contrato no OTRS:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            config: {
              url: error.config?.url,
              method: error.config?.method,
              timeout: error.config?.timeout
            },
            payload: contractPayload
          });
          setLoading(false);
          message.error(`Erro na criação do contrato no OTRS: ${error.response?.data?.message || error.message}`);
        });
    } catch (error) {
      console.error('❌ Erro no processamento do contrato:', error);
      message.error('Erro no processamento do contrato. Verifique os dados e tente novamente.');
      setLoading(false);
    }
    } catch (dateError) {
      console.error('❌ Erro no processamento das datas:', dateError);
      message.error('Erro no processamento das datas. Verifique as datas selecionadas.');
      setLoading(false);
    }
  };

  const showModal = async () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModalVisible(false);
  };

  const isInstallmentsDisabled = !contractValue || parseFloat(contractValue.toString().replace(/[^0-9.-]+/g, "")) === 0;
  return (
    <>
      <Button type="primary" onClick={showModal}>
        Cadastrar Contrato
      </Button>
      <Modal
        title="Cadastre um Contrato"
        open={isModalVisible}
        onOk={handleOk}
        okText="Adicionar Contrato"
        cancelText="Cancelar"
        okButtonProps={{ loading: loading }}
        onCancel={handleCancel}
      >
        <Form
          requiredMark={false}
          layout="vertical"
          onFinish={handleSubmit}
          form={form}
        >
          <Form.Item name="crm_id" label="CRM ID">
            <Input placeholder="CRM ID" />
          </Form.Item>
          <Form.Item
            label={<RequiredLabelForm title="Nome do Contrato" />}
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="name"
          >
            <Input placeholder="Nome do Contrato" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="value"
            label={<RequiredLabelForm title="Valor do Contrato" />}
          >
            <Input placeholder="Valor do Contrato" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="installments"
            label={<RequiredLabelForm title="Número de Parcelas" />}
            initialValue={1}
          >
            <InputNumber min={1} max={12} step={1} style={{ width: '100%' }} placeholder="Número de Parcelas" disabled={isInstallmentsDisabled} />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="type_hours"
            label={<RequiredLabelForm title="Tipo de Consumo" />}
          >
            <Select
              placeholder="Selecione o Tipo de Consumo"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children?.toLowerCase().indexOf(input?.toLowerCase()) >=
                0
              }
            >
              <Option value="Mensal">Mensal</Option>
              <Option value="Trimestral">Trimestral</Option>
              <Option value="Semestral">Semestral</Option>
              <Option value="Anual">Anual</Option>
              <Option value="Projeto">Projeto</Option>
              <Option value="Ondemand">OnDemand</Option>
              <Option value="Ilimitado">Ilimitado</Option>
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="expected_start_date"
            label={<RequiredLabelForm title="Duração do Contrato" />}
          >
            <RangePicker
              style={{ width: "100%" }}
              placeholder={["Início do Contrato", "Fim do Contrato"]}
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="total_hours"
            label={<RequiredLabelForm title="Total de Horas" />}
          >
            <Input placeholder="Total de Horas" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="pool_type"
            label={<RequiredLabelForm title="Tipo de horas" />}
          >
            <Select placeholder="Selecione o tipo de Horas...">
              <Option value="pool_8_5">Pool 8x5</Option>
              <Option value="pool_24_7">Pool 24x7</Option>
              <Option value="pool_setup_8_5">Pool Setup 8x5</Option>
              <Option value="pool_setup_24_7">Pool Setup 24x7</Option>
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="customer"
            label={<RequiredLabelForm title="Cliente" />}
          >
            <Select
              placeholder="Selecione um cliente"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children?.toLowerCase().indexOf(input?.toLowerCase()) >=
                0
              }
              loading={loadingClients}
            >
              {clients.data
                ?.filter(
                  (i) =>
                    i?.identifications?.itsm_id &&
                    (i?.names?.fantasy_name || i?.names?.name)
                )
                .map((e, index) => {
                  return (
                    <Option
                      key={index}
                      value={JSON.stringify({
                        id: e?.id,
                        itsm_id: e.identifications?.itsm_id,
                      })}
                    >
                      {e.names?.name
                        ? e.names.name + " - " + e.names.fantasy_name
                        : e.names.fantasy_name}
                    </Option>
                  );
                })}
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            label={<RequiredLabelForm title="Valor/Hora" />}
            name="excess_cost"
          >
            <Input placeholder="Valor/Hora" />
          </Form.Item>
          <Form.Item label="Calculadora AWS" name="aws_value">
            <Input placeholder="Valor da Calculadora AWS" />
          </Form.Item>
          <Form.Item label="Link da Calculadora AWS" name="aws_calculator_link">
            <Input placeholder="Link da Calculadora AWS" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            label={<RequiredLabelForm title="Escopo" />}
            name="scope"
          >
            <Input.TextArea
              placeholder="Insira o escopo do contrato"
              style={{ height: "auto", minHeight: "auto" }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
