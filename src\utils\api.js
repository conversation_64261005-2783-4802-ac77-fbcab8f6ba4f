import axios from 'axios'
import { authService } from '../services/authService'
// import { httpOnlyAuthService } from '../services/httpOnlyAuthService' // Removido - não existe mais
import { config } from './config'
import { logger } from './logger'

export const URLS = {
    DSM: process.env.REACT_APP_API_PERMISSION,
    PROPOSALS: process.env.REACT_APP_API_PROPOSALS,
    OTRS: process.env.REACT_APP_API_OTRS_BASE_URL,
    REPORTS: process.env.REACT_APP_API_REPORTS_URL
}

// Instância para DSM API
export const apiDsm = axios.create({
    baseURL: URLS.DSM,
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})

// Interceptor para adicionar token automaticamente
if (apiDsm && apiDsm.interceptors) {
    apiDsm.interceptors.request.use(
        (config) => {
            // Tentar obter token do httpOnlyAuthService primeiro
            let token = null;

        try {
            // Sistema original: usar token do localStorage
            token = authService.getToken();
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
                logger.debug('Usando token do localStorage para DSM API');
            }
        } catch (error) {
            logger.warn('Erro ao configurar autenticação para DSM API:', error);
        }

        logger.debug('DSM API Request:', {
            method: config.method?.toUpperCase(),
            url: config.url,
            hasAuth: !!token || config.withCredentials
        });

        return config;
    },
    (error) => {
        logger.error('Erro no interceptor de request da DSM API:', error);
        return Promise.reject(error);
    }
);
}

// Interceptor de response para logging
if (apiDsm && apiDsm.interceptors) {
    apiDsm.interceptors.response.use(
    (response) => {
        // logger.debug('DSM API Response:', {
        //     status: response.status,
        //     url: response.config?.url
        // });
        return response;
    },
    (error) => {
        logger.error('DSM API Error:', {
            status: error.response?.status,
            url: error.config?.url,
            message: error.message
        });
        return Promise.reject(error);
    }
);
}

export const apiProposals = axios.create({
    baseURL: URLS.PROPOSALS
})

export const apiOTRS = axios.create({
    baseURL: URLS.OTRS,
    timeout: 45000, // 45 segundos para OTRS (mais tempo que DSM)
    headers: {
        Authorization: config.OTRS.AUTH,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})

// Interceptor para retry automático em falhas de rede OTRS
apiOTRS.interceptors.response.use(
    (response) => {
        logger.debug('OTRS API Response:', {
            status: response.status,
            url: response.config?.url
        });
        return response;
    },
    async (error) => {
        const config = error.config;

        // Retry logic para OTRS
        if (!config._retry && shouldRetryOtrsRequest(error)) {
            config._retry = true;
            config._retryCount = (config._retryCount || 0) + 1;

            if (config._retryCount <= 2) { // Máximo 2 retries
                logger.warn(`Retry ${config._retryCount}/2 para OTRS:`, {
                    url: config.url,
                    error: error.message
                });

                // Aguardar antes do retry
                await new Promise(resolve => setTimeout(resolve, 1000 * config._retryCount));
                return apiOTRS(config);
            }
        }

        logger.error('OTRS API Error:', {
            status: error.response?.status,
            url: error.config?.url,
            message: error.message,
            retryCount: config._retryCount || 0
        });

        return Promise.reject(error);
    }
);

// Função para determinar se deve fazer retry para OTRS
const shouldRetryOtrsRequest = (error) => {
    const status = error.response?.status;
    // Retry para erros de servidor (5xx), timeout e erros de rede
    return !status || status >= 500 || error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND';
};

export const apiReports = axios.create({
    baseURL: URLS.REPORTS
})

export const getHeader = () => {
    return authService.getAuthHeaders();
}