import { message } from "antd";
import { v4 } from "uuid";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { s3CopyObjects } from "../../service/apiDsmS3";
import { setServicesState } from "../../store/actions/services-action";

import {
  setAllTechnicalProposalState,
  setTechnicalProposalState,
} from "../../store/actions/technical-proposal-action";
import { store } from "../../store/store";
import { apiDsm, getHeader } from "../../utils/api";
import { scrollToElement } from "../../utils/scroll-to-element";
import { totalValuesInitialState } from "../../constants/totalValuesInitialState";
import { calculateManagementServices } from "./calculateManagementServices";
import { totalValuesController } from "./newTotalValuesTechnical";
import axios from "axios";
import * as customerController from "../clients/clientsController";
import { templateProposalsFields } from "../../constants/proposalsFields";
import { dsmProvider } from "../../provider/dsm-provider";

function getCustomerName(customer) {
  let clientName = "";
  if (customer.names) {
    if (customer.names.fantasy_name && customer.names.fantasy_name !== "")
      clientName = customer.names.fantasy_name;
    else clientName = customer.names.name;
  }

  if (customer.name) clientName = customer.name;

  return clientName;
}

export function updatePersistData(persistData) {
  const headers = getHeader();

  const payloadStr = JSON.stringify(persistData);
  let payload = JSON.parse(payloadStr);
  payload["id"] = payload.persistID;

  delete payload.serviceList;
  delete payload.loading;

  const localPersistID = localStorage.getItem("persist-proposal-id");
  if (localPersistID && localPersistID === payload.persistID) {
    apiDsm
      .put("proposals/persit", payload, { headers })
      .then((response) => {})
      .catch((error) => {
        console.log("Não foi possivel realizar o auto save das informações");
        console.log(error);
      });
  }
}

export async function existPersistData(persistID) {
  try {
    const tableName = `${process.env.REACT_APP_STAGE}-proposal-persist`;
    const headers = { ...getHeader(), dynamodb: tableName };

    return await apiDsm
      .get(`read/id/${persistID}`, { headers })
      .then((response) => {
        const { data } = response.data;
        const persistProposal = data.Item;

        if (!persistProposal) return null;

        return persistProposal;
      })
      .catch((error) => {
        return null;
      });
  } catch (error) {
    return null;
  }
}

export async function getPersistData(
  services,
  editProposalPersistID = null,
  navigate
) {
  try {
    let { persistID } = store.getState().technicalProposal;
    const isSuccess = true;
    const tableName = `${process.env.REACT_APP_STAGE}-proposal-persist`;
    const headers = { ...getHeader(), dynamodb: tableName };

    if (editProposalPersistID) persistID = editProposalPersistID;

    if (!persistID) {
      console.log("No persistID found, skipping API call");
      return !isSuccess;
    }

    return apiDsm
      .get(`read/id/${persistID}`, { headers })
      .then((response) => {
        const { data } = response.data;
        const persistProposal = data.Item;

        if (!persistProposal) return !isSuccess;

        if (persistProposal.proposalID && !editProposalPersistID) {
          navigate(`technical-proposals/edit/${persistProposal.proposalID}`);
          return true;
        }

        const serviceList = getServiceListWithChange(
          services,
          persistProposal.selectedServices
        );

        setAllTechnicalProposalState({
          ...persistProposal,
          readyToPersist: true,
          serviceList: serviceList,
        });

        return isSuccess;
      })
      .catch((error) => {
        console.log(error);
        console.log("no data persists");

        return !isSuccess;
      });
  } catch (error) {
    console.log(error);
    return false;
  }
}

export async function deletePersistData(id = "") {
  let isRemoved = true;
  try {
    let { persistID } = store.getState().technicalProposal;
    const tableName = `${process.env.REACT_APP_STAGE}-proposal-persist`;
    const headers = { ...getHeader(), dynamodb: tableName };

    if (id) persistID = id;

    if (!id) {
      const localPersistData = localStorage.getItem("persist-proposal-id");
      persistID = localPersistData;
    }

    if (persistID) await apiDsm.delete(`delete/${persistID}`, { headers });

    return isRemoved;
  } catch (error) {
    console.log(error);
    isRemoved = false;
    return isRemoved;
  }
}

export function removeAllDataFromLocalStorage() {
  const { selectedServices } = store.getState().technicalProposal;

  localStorage.removeItem("persist-proposal-id");

  selectedServices.forEach((serviceID) => {
    localStorage.removeItem(`proposal-service-${serviceID}`);
  });
}

const formatTechnicalProposalState = (
  isTemplate,
  proposal,
  servicesCatalog,
  selectedServices,
  persistID
) => {
  let formattedTechnicalProposalState = {};
  let loopParam = templateProposalsFields;
  for (let i = 0; i < loopParam.length; i++) {
    let value = templateProposalsFields[i].value;
    let objectName = templateProposalsFields[i].objectName;
    if (objectName === "serviceList") {
      formattedTechnicalProposalState[objectName] = servicesCatalog;
    } else if (objectName === "selectedServices") {
      formattedTechnicalProposalState[objectName] = selectedServices;
    } else if (objectName === "persistID") {
      formattedTechnicalProposalState[objectName] = persistID;
    } else if (proposal.specialFields.find((f) => f.name === value)) {
      formattedTechnicalProposalState[objectName] = proposal.specialFields.find(
        (f) => f.name === value
      ).value;
    } else {
      formattedTechnicalProposalState[objectName] =
        value && value !== "" && value?.length
          ? proposal[templateProposalsFields[i].value]
          : value;
    }
  }

  return formattedTechnicalProposalState;
};

export async function getProposal(
  proposalID,
  serviceList,
  isTemplate = false,
  navigate
) {
  const servicesCatalog = serviceList;
  try {
    const isSuccess = true;
    const tableName = `${process.env.REACT_APP_STAGE}-proposals`;
    const headers = { ...getHeader(), dynamodb: tableName };

    const response = await apiDsm
      .get(`read/id/${proposalID}`, { headers })
      .then(async (response) => {
        const { data } = response.data;
        const proposal = data.Item;

        let persistID = proposal.searchId;

        const services = servicesCatalog.map((item) => {
          let newItem = item;

          const proposalServices = proposal.services.find(
            (s) => s.id === item.id
          );

          if (proposalServices) {
            newItem = proposalServices;
          }

          return newItem;
        });

        const existPersistData = await getPersistData(
          services,
          persistID,
          navigate
        );

        if (existPersistData) {
          localStorage.setItem("persist-proposal-id", persistID);
          return isSuccess;
        }

        const selectedServices = proposal.services.map((service) => service.id);

        proposal.services.forEach((proposalService) => {
          const index = services.findIndex((s) => s.id === proposalService.id);

          if (index >= 0) services[index] = proposalService;
        });

        const challenges =
          proposal.specialFields.find((field) => field.name === "Desafios") ||
          "";
        const premisses =
          proposal.specialFields.find((field) => field.name === "Premissas") ||
          "";
        const architecture =
          proposal.specialFields.find(
            (field) => field.name === "Arquitetura"
          ) || "";
        const observations =
          proposal.specialFields.find(
            (field) => field.name === "Observações"
          ) || "";
        const scenarios =
          proposal.specialFields.find(
            (field) => field.name === "Cenário apresentado"
          ) || "";
        const internal_notes =
          proposal.specialFields.find(
            (field) => field.name === "Notas Internas"
          ) || "";
        const additional_costs =
          proposal.specialFields.find(
            (field) => field.name === "Custos adicionais"
          ) || "";
        const main_factors =
          proposal.specialFields.find(
            (field) => field.name === "Fatores influenciadores"
          ) || "";
        const extra_points =
          proposal.specialFields.find(
            (field) => field.name === "Pontos não contemplados"
          ) || "";
        const expected_results =
          proposal.specialFields.find(
            (field) => field.name === "Resultados esperados"
          ) || "";

        const customers = await getCustomers();

        const clientName = getCustomerName(proposal.customer);
        let selectedCustomer = {};
        if (proposal?.customer?.cnpj && proposal?.customer?.cnpj !== "") {
          selectedCustomer = customers.filter(
            (c) => c?.cnpj === proposal.customer.cnpj
          )[0];
          if (!selectedCustomer) {
            selectedCustomer = customers.filter(
              (c) => c?.names?.fantasy_name === clientName
            )[0];
            if (!selectedCustomer) {
              selectedCustomer = customers.filter(
                (c) => c?.names?.name === clientName
              );
              if (!selectedCustomer) {
                selectedCustomer = customers.filter(
                  (c) => c?.name === clientName
                );
              }
            }
          }
        } else {
          selectedCustomer = customers.filter(
            (c) => c?.names?.fantasy_name === clientName
          )[0];
          if (!selectedCustomer) {
            selectedCustomer = customers.filter(
              (c) => c?.names?.name === clientName
            );
            if (!selectedCustomer) {
              selectedCustomer = customers.filter(
                (c) => c?.name === clientName
              );
            }
          }
        }

        selectedCustomer.contacts = proposal.customer.contacts;

        let technicalProposalState = null;

        const serviceList = getServiceListWithChange(
          services,
          selectedServices
        );

        if (isTemplate) {
          persistID = v4();

          const sourceBucketName = `${process.env.REACT_APP_STAGE}-proposals-documents`;
          const destinationBucketName = `${process.env.REACT_APP_STAGE}-proposals-documents`;

          for (const f of proposal.fileNames) {
            try {
              const fileKey = `/${proposal.searchId}/documents/${f.name}`;
              const fileName = `${persistID}/documents/${f.name}`;

              await s3CopyObjects({
                sourceBucketName: sourceBucketName,
                destinationBucketName: destinationBucketName,
                fileKey: fileKey,
                fileName: fileName,
              });
            } catch (error) {
              console.error(`❌ Erro ao copiar documento ${f.name}:`, error);
            }
          }

          for (const f of proposal.architectureFileNames) {
            try {
              const fileKey = `/${proposal.searchId}/documents/architecture/${f.name}`;
              const fileName = `${persistID}/documents/architecture/${f.name}`;

              await s3CopyObjects({
                sourceBucketName: sourceBucketName,
                destinationBucketName: destinationBucketName,
                fileKey: fileKey,
                fileName: fileName,
              });
            } catch (error) {
              console.error(`❌ Erro ao copiar arquivo de arquitetura ${f.name}:`, error);
            }
          }

          for (const f of proposal.scenarioFileNames) {
            try {
              const fileKey = `/${proposal.searchId}/documents/scenarios/${f.name}`;
              const fileName = `${persistID}/documents/scenarios/${f.name}`;

              await s3CopyObjects({
                sourceBucketName: sourceBucketName,
                destinationBucketName: destinationBucketName,
                fileKey: fileKey,
                fileName: fileName,
              });
            } catch (error) {
              console.error(`❌ Erro ao copiar arquivo de cenário ${f.name}:`, error);
            }
          }


          technicalProposalState = formatTechnicalProposalState(
            isTemplate,
            proposal,
            servicesCatalog,
            selectedServices,
            persistID
          );
        } else {
          technicalProposalState = {
            readyToPersist: true,
            contacts: selectedCustomer.contacts || "",
            bus: proposal.bus,
            selectedContact: proposal.customer.mainContact,
            type: proposal.type,
            clientName: clientName,
            status: proposal.status,
            proposalID: proposal.id,
            projectName: proposal.name,
            changesServicesSelected: [],
            persistID: persistID,
            calculator: proposal.awsCosts,
            architects: proposal.architects,
            selectedServices: selectedServices,
            opportunities: proposal.opportunity,
            selectedOpportunity: proposal.mainOportunity,
            scenarios: scenarios ? scenarios.value : "",
            premisses: premisses ? premisses.value : "",
            challenges: challenges ? challenges.value : "",
            extra_points: extra_points ? extra_points.value : "",
            main_factors: main_factors ? main_factors.value : "",
            observations: observations ? observations.value : "",
            architecture: architecture ? architecture.value : "",
            internal_notes: internal_notes ? internal_notes.value : "",
            additional_costs: additional_costs ? additional_costs.value : "",
            expected_results: expected_results ? expected_results.value : "",

            serviceList: serviceList,
            selectedCustomer: selectedCustomer,

            fileNames: proposal.fileNames,
            totalValues: proposal.totalValues,
            architectureFileNames: proposal.architectureFileNames,
            scenarioFileNames: proposal.scenarioFileNames,

            ticketData: proposal.ticketData,
            commercialStatus: proposal.commercialStatus,
            active: proposal.active,
            monthSelected: proposal.monthSelected,
            spreadSetup: proposal.spreadSetup,

            pipedriveDealOwner: proposal?.pipedriveDealOwner
              ? proposal?.pipedriveDealOwner
              : null,
          };
        }

        setAllTechnicalProposalState(technicalProposalState);
        localStorage.setItem("persist-proposal-id", persistID);

        return isSuccess;
      })
      .catch((error) => {
        console.log(error);
        message.error("Não foi possivel carregar os dados desta proposta");
        return !isSuccess;
      });

    return response;
  } catch (error) {
    console.log(error);
    message.error("Não foi possivel carregar os dados desta proposta");
    return false;
  }
}

export async function getCustomers() {
  try {
    const customerRoute = "customers/read/hasActiveContracts";
    const status = { status: 1 };

    const tableCustomers = await customerController.getCustomerByParameter(
      customerRoute,
      status
    );

    const filteredByExistingName = tableCustomers.filter(
      (c) => c?.names?.name || c?.names?.fantasy_name
    );

    return filteredByExistingName;
  } catch (error) {
    return [];
  }
}

export async function getServices() {
  try {
    const services = await dynamoGet(`${process.env.REACT_APP_STAGE}-services`);

    setServicesState(services);

    return services;
  } catch (error) {
    return [];
  }
}

export async function calculateTotalValues(services, costs) {
  const totalValuesInitialStateStr = JSON.stringify(totalValuesInitialState);
  const totalValuesInitialStateObj = JSON.parse(totalValuesInitialStateStr);

  totalValuesInitialStateObj.forEach(async (hourValueObj) => {
    const hourClass =
      hourValueObj.hourClass === "hourValue"
        ? "daredeHourValue"
        : "daredeAdditionalHours";

    services.forEach((service) => {
      const estimates = calculateEstimatesFromService(service);

      hourValueObj.values.forEach((hours, indexHours) => {
        hours.values.forEach((type, typeIndex) => {
          if (costs) {
            const costsOfMonth = costs[hourClass].find(
              (cost) => cost.month === hours.month
            );
            if (costsOfMonth) {
              const hourValue = costsOfMonth.values.find(
                (c) => c.name === type.formName
              );

              type.hourValue = hourValue ? hourValue.value : 0;
            }
          }

          if (hourClass === "daredeHourValue") {
            type.hours += estimates[type.formName] || 0;
            type.totalHours = type.hours + type.burst || 0;
            type.totalValue = type.totalHours * type.hourValue;
          }
        });
      });
    });
  });

  const { technicalProposal } = store.getState();

  let newServices = await calculateManagementServices(services);

  const newTotalValues = await totalValuesController(
    technicalProposal,
    costs,
    newServices
  );

  setTechnicalProposalState({
    field: "totalValues",
    value: newTotalValues,
  });
}

function calculateEstimatesFromService(service) {
  const estimatesService = service.tasks.reduce(
    (estimate, task) => {
      estimate["setup8x5"] += task.estimates["8x5setup"];
      estimate["setup24x7"] += task.estimates["24x7setup"];
      estimate["sust8x5"] += task.estimates["8x5sust"];
      estimate["sust24x7"] += task.estimates["24x7sust"];
      estimate["dbasec8x5"] += task.estimates["8x5dbasec"];
      estimate["dbasec24x7"] += task.estimates["24x7dbasec"];

      return estimate;
    },
    {
      setup8x5: 0,
      setup24x7: 0,
      sust8x5: 0,
      sust24x7: 0,
      dbasec8x5: 0,
      dbasec24x7: 0,
    }
  );

  return estimatesService;
}

function getServiceListWithChange(services, selectedServices) {
  const serviceListStr = JSON.stringify(services);
  const servicesObj = JSON.parse(serviceListStr);

  selectedServices.forEach((serviceID) => {
    const localStorageName = `proposal-service-${serviceID}`;
    const localChangeService = localStorage.getItem(localStorageName);

    if (localChangeService) {
      const changeService = JSON.parse(localChangeService);
      const index = servicesObj.findIndex((d) => d.id === serviceID);
      if (index >= 0) servicesObj[index] = changeService;
    }
  });

  return servicesObj;
}

export function validateAllFields() {
  const { technicalProposal } = store.getState();

  if (!technicalProposal.projectName) {
    scrollToElement("projectName");
    message.warning("Nome do Projeto não informado");
    return false;
  }

  if (!technicalProposal.clientName) {
    scrollToElement("clientName");
    message.warning("Nenhum cliente selecionado");
    return false;
  }

  if (!technicalProposal.type) {
    scrollToElement("type");
    message.warning("Tipo não informado");
    return false;
  }

  if (!technicalProposal.status) {
    scrollToElement("status");
    message.warning("Status não informado");
    return false;
  }

  if (technicalProposal.selectedContact.length === 0) {
    scrollToElement("selectedContact");
    message.warning("Nenhum contato selecionado");
    return false;
  }

  if (technicalProposal.architects.length === 0) {
    scrollToElement("architects");
    message.warning("Nenhum arquiteto selecionado");
    return false;
  }

  if (technicalProposal.bus.length === 0) {
    scrollToElement("bus");
    message.warning("Nenhuma BU selecionado");
    return false;
  }

  if (!technicalProposal.selectedOpportunity) {
    scrollToElement("selectedOpportunity");
    message.warning("Nenhuma Oportunidade selecionada");
    return false;
  }

  if (technicalProposal.additional_costs?.length > 0) {
    let field = "";

    const isInvalid = technicalProposal.additional_costs.some((cost) => {
      if (!cost.title) {
        field = "Titulo";
        return true;
      }

      if (!cost.month_value) {
        field = "Valor mensal";
        return true;
      }

      if (!cost.year_value) {
        field = "Valor anual";
        return true;
      }
    });

    if (isInvalid) {
      scrollToElement("additional-costs");
      message.warning(`Custos Adicionais - ${field} não informado`);
      return false;
    }
  }

  if (technicalProposal.calculator.length > 0) {
    let field = "";

    const isInvalid = technicalProposal.calculator.some((calc) => {
      if (!calc.title) {
        field = "Titulo";
        return true;
      }

      if (!calc.month_value) {
        field = "Valor mensal";
        return true;
      }

      if (!calc.year_value) {
        field = "Valor anual";
        return true;
      }

      if (!calc.calculator_link) {
        field = "Link";
        return true;
      }
    });

    if (isInvalid) {
      scrollToElement("calculate");
      message.warning(`Calculadora - ${field} não informado`);
      return false;
    }
  }

  return true;
}

const getProposalsByStatus = async (status) => {
  try {
    const tableName = `${process.env.REACT_APP_STAGE}-proposals`;
    const allProposals = await dynamoGet(tableName);

    const filteredProposals = allProposals.filter(proposal =>
      proposal.status === status
    );

    return filteredProposals;
  } catch (error) {
    console.log(error);
    const { status } = error.response;
    if (status === 404) {
      return [];
    }

    return null;
  }
};

export const getAllProposalsByStatus = async (
  setAllProposals,
  setLoading,
  setParallelLoading,
  proposalsStatus
) => {
  let proposalDataByStatus = [];

  try {
    for (let i = 0; i < proposalsStatus.length; i++) {

      if (proposalsStatus[i] === "em andamento") {
        try {
          proposalDataByStatus = await getProposalsByStatus(proposalsStatus[i]);
          setAllProposals((prevState) => [...prevState, ...proposalDataByStatus]);
        } catch (error) {
          setAllProposals((prevState) => [...prevState]);
        }
      } else {
        try {
          proposalDataByStatus = await getProposalsByStatus(proposalsStatus[i]);
          if (proposalDataByStatus)
            setAllProposals((prevState) => [...prevState, ...proposalDataByStatus]);
          else setAllProposals((prevState) => [...prevState]);
        } catch (error) {
          setAllProposals((prevState) => [...prevState]);
        }
      }
    }
  } catch (error) {
    console.log("❌ Erro geral no carregamento de propostas:", error);
  } finally {
    setLoading(false);
    setParallelLoading(false);
  }
};

export async function getAllProposals() {
  let { data, nextPage } = await getPaginateProposals();
  while (nextPage) {
    let response = await getPaginateProposals(nextPage);
    nextPage = response.nextPage;
    data.push(...response.data);
  }

  return {
    Items: data,
  };
}

export async function getPaginateProposals(nextPage = "") {
  const jwt = localStorage.getItem("jwt");
  const provider = dsmProvider();
  let params = [];
  if (nextPage) params.push(`?nextPage=${nextPage}`);

  const { data } = await provider.get(
    `${process.env.REACT_APP_API_PERMISSION}read/paginate${params.join("&")}`,
    {
      headers: {
        dynamodb: `${process.env.REACT_APP_STAGE}-proposals`,
      },
    }
  );

  return {
    data: data.data,
    nextPage: data.nextPage,
  };
}
