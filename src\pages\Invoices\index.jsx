import React, { useEffect, useState } from "react";
import { useSelector, shallowEqual } from "react-redux";
import { differenceInYears, getMonth, getYear } from "date-fns";
import dayjs from "dayjs";
import "dayjs/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

import { Row, Col, Typography, DatePicker, Select } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

import * as controller from "../../controllers/invoices/invoiceController";
import * as clientController from "../../controllers/clients/clientsController";

import {
  setInvoiceFieldState,
  setInvoicesDateRange,
} from "../../store/actions/invoice-action";
import { INVOICE_VIEW_STATES } from "../../store/reducers/invoices-reducer";

import { DSMLayout } from "../../components/Layout";
import { UploadModal } from "../../components/Modals/Invoices/UploadModal";

import { DolarInvoice } from "./components/dolar-invoice";
import { ExportInvoicesCSV } from "./components/exportCSV";
import { FilterInvoice } from "./components/filter-invoice";
import { InvoiceTable } from "./components/table-invoice";
import { ConfigModal } from "../../components/Modals/Invoices/ConfigModal";

const { Text } = Typography;

export const Invoices = () => {
  const invoices = useSelector((state) => state.invoice.invoices, shallowEqual);
  const dateRange = useSelector(
    (state) => state.invoice.dateRange,
    shallowEqual
  );
  const [filterLoading, setFilterLoading] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });

    const month = getMonth(new Date()) + 1;
    const year = getYear(new Date());

    initPage({ month, year });
  }, []);

  async function initPage({ month, year, dateChange = false }) {
    if (invoices?.length > 0 && !dateChange) return;
    try {
      setFilterLoading(true);
      setInvoiceFieldState({ field: "loading", value: true });
      setInvoicesDateRange({ month, year });
      const promisses = await Promise.allSettled([
        controller.getPermission(),
        controller.getDolarValue({ month, year }),
      ]);

      const dolar = promisses[1].value || "0";

      let invoices = await controller.getInvoices({
        month,
        year,
        dolar,
        customers: [],
      });



      setInvoiceFieldState({ field: "loading", value: false });
      if (invoices.length > 0) {
        const customers = await clientController.getAllCustomersV2();
        invoices = JSON.parse(JSON.stringify(invoices));
        invoices.forEach((invoice) => {
          const customer = customers.find(
            (c) =>
              Array.isArray(c.accounts) &&
              c.accounts.some(
                (a) =>
                  a.account_id === invoice.sub_account ||
                  a.account_id === invoice.bill_payer_account_id
              )
          );

          invoice.customer = "";
          if (customer)
            invoice.customer =
              customer.names.name || customer.names.fantasy_name;
          else invoice.customer = "false";
        });

        setInvoiceFieldState({ field: "invoices", value: invoices });
      }

      setFilterLoading(false);
    } catch (error) {
      console.log(error);
      setInvoiceFieldState({ field: "loading", value: false });
    }
  }

  return (
    <DSMLayout>
      <ConfigModal />
      <Row justify="space-between" style={{ marginBottom: "1em" }}>
        <DolarInvoice />
        <Col md={12}>
          <Row justify="end" style={{ marginRight: 16 }}>
            <Col md={8}>
              <UploadDolarModal />
            </Col>
            <Col md={14} style={{ left: 16 }}>
              <ExportInvoicesCSV />
            </Col>
          </Row>
        </Col>
      </Row>
      <Row justify="space-between" style={{ marginBottom: "1em" }}>
        <Col md={10}>
          <Text>Pesquisar: </Text>
          <FilterInvoice />
        </Col>

        <Col md={6}>
          <Text>Filtrar: </Text>
          <DatePicker
            placeholder="Selecione o mês"
            picker="month"
            locale={locale}
            disabled={filterLoading}
            disabledDate={(current) => {
              if (!current) return false;

              // Permitir apenas os últimos 13 anos até o mês atual
              const thirteenYearsAgo = dayjs().subtract(13, 'years').startOf('month');
              const currentMonth = dayjs().endOf('month');

              return current < thirteenYearsAgo || current > currentMonth;
            }}
            style={{ width: "100%" }}
            value={controller.formatDateRange(dateRange)}
            onChange={(selectedDate) => {
              if (!selectedDate) return;

              console.log('🎯 Invoices: Data selecionada:', {
                selectedDate: selectedDate.format('YYYY-MM-DD'),
                month: selectedDate.month() + 1,
                year: selectedDate.year()
              });

              // Verificar se a data está dentro do limite permitido
              const thirteenYearsAgo = dayjs().subtract(13, 'years');
              if (selectedDate.isBefore(thirteenYearsAgo)) {
                console.warn('⚠️ Invoices: Data muito antiga selecionada');
                return;
              }

              initPage({
                month: selectedDate.month() + 1, // dayjs months são 0-indexed
                year: selectedDate.year(),
                dateChange: true,
              });
            }}
          />
          {filterLoading && (
            <div style={{ position: "absolute", top: "50%", left: "75%" }}>
              <LoadingOutlined spin />
            </div>
          )}
        </Col>

        <Col md={5}>
          <Text>Visualização: </Text>
          <ViewOptions />
        </Col>
      </Row>
      <InvoiceTable />
    </DSMLayout>
  );
};

const ViewOptions = () => {
  const view = useSelector((state) => state.invoice.view, shallowEqual);

  return (
    <Select
      defaultValue={INVOICE_VIEW_STATES.SUMMARY}
      value={view}
      onChange={(e) => setInvoiceFieldState({ field: "view", value: e })}
      style={{ width: "100%" }}
    >
      <Select.Option value={INVOICE_VIEW_STATES.SUMMARY}>
        Sumarizada
      </Select.Option>
      <Select.Option value={INVOICE_VIEW_STATES.DETAILD}>
        Detalhada
      </Select.Option>
    </Select>
  );
};

const UploadDolarModal = () => {
  const permissions = useSelector(
    (state) => state.invoice.permissions,
    shallowEqual
  );

  return permissions
    .map((permission) => {
      return permission.code;
    })
    .includes("upload_invoice") ? (
    <UploadModal />
  ) : null;
};
