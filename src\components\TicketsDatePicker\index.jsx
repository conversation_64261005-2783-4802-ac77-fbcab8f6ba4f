import React from "react";
import { DatePicker, message } from "antd";
import dayjs from "dayjs";
import "dayjs/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";
import { differenceInCalendarDays } from "date-fns";

const { RangePicker } = DatePicker;

export const TicketsDatePicker = ({
  value,
  onChange,
  onError,
  placeholder = ["Data inicial", "Data final"],
  errorStatus = false
}) => {

  console.log('🎯 TicketsDatePicker RENDERIZADO!', {
    value,
    errorStatus,
    dayjsNow: dayjs().format('YYYY-MM-DD'),
    dayjsYear: dayjs().year()
  });
  
  const handleChange = (dates) => {
    console.log('🎯 TicketsDatePicker onChange:', {
      dates,
      isArray: Array.isArray(dates),
      length: dates?.length,
      start: dates?.[0]?.format('YYYY-MM-DD'),
      end: dates?.[1]?.format('YYYY-MM-DD')
    });

    if (!dates || dates.length !== 2) {
      // Limpar seleção
      onChange(null);
      return;
    }

    const [startDate, endDate] = dates;
    
    // Validar se as datas são válidas
    if (!startDate.isValid() || !endDate.isValid()) {
      message.error("Datas inválidas selecionadas");
      onError?.(true);
      return;
    }

    // Calcular diferença em dias
    const daysDiff = endDate.diff(startDate, 'day');
    
    console.log('🎯 Diferença em dias:', daysDiff);

    if (daysDiff > 5) {
      message.error("Período inválido, apenas até 5 dias");
      onError?.(true);
      return;
    }

    if (daysDiff < 0) {
      message.error("Data inicial deve ser anterior à data final");
      onError?.(true);
      return;
    }

    // Sucesso - chamar callback
    onError?.(false);
    onChange(dates);
  };

  const getCurrentValue = () => {
    if (errorStatus) {
      return null;
    }

    if (value && Array.isArray(value) && value.length === 2) {
      const [start, end] = value;
      
      // Garantir que são objetos dayjs válidos
      const startDayjs = dayjs.isDayjs(start) ? start : dayjs(start);
      const endDayjs = dayjs.isDayjs(end) ? end : dayjs(end);
      
      if (startDayjs.isValid() && endDayjs.isValid()) {
        return [startDayjs, endDayjs];
      }
    }

    return null;
  };

  return (
    <RangePicker
      style={{ width: "100%" }}
      locale={locale}
      format="DD/MM/YYYY"
      placeholder={errorStatus ? ["Período inválido", "Período inválido"] : placeholder}
      status={errorStatus ? "error" : ""}
      value={getCurrentValue()}
      onChange={handleChange}
      allowClear={true}
      showTime={false}
      getPopupContainer={(trigger) => trigger.parentElement}
      disabledDate={(current) => {
        // Limitar seleção entre 2 anos atrás e a data atual
        const twoYearsAgo = dayjs().subtract(2, 'years').startOf('day');
        const today = dayjs().endOf('day');
        return current && (current.isBefore(twoYearsAgo) || current.isAfter(today));
      }}
      // Forçar o calendário a abrir no ano atual
      defaultPickerValue={[dayjs(), dayjs()]}
      // Configurações adicionais para garantir funcionamento correto
      inputReadOnly={false}
      autoFocus={false}
    />
  );
};

export default TicketsDatePicker;
