import { React } from "react";
import {
  Select,
  DatePicker,
  But<PERSON>,
  Form,
  Typography,
  Row,
  Col,
} from "antd";
import locale from "antd/es/date-picker/locale/pt_BR";
import moment from "moment";
import { CloseOutlined } from "@ant-design/icons";

function BornupFilter(props) {
  const { Title } = Typography;
  const { allContracts, allWallets, handlePopover, setFilterData } = props;
  const { Option } = Select;
  const [form] = Form.useForm();

  function onFilterFailed() { }

  function clear() {
    form.resetFields();
  }


  const sortAlphaObj = (array) => {
    const sortedArray = array.sort((a, b) => {
      if (a.name.toLowerCase() > b.name.toLowerCase()) {
        return 1;
      }
      if (a.name.toLowerCase() < b.name.toLowerCase()) {
        return -1;
      }
      return 0;
    });
    return sortedArray;
  };

  function onFilterFinish(values) {
    setFilterData(values)
    handlePopover()
  }
  return (
    <>
      <Row justify="space-between">
        <Col>
          <Title level={5} style={{ fontWeight: 400 }}>
            Gráfico de Burnup
          </Title>
        </Col>
        <Col>
          <CloseOutlined onClick={() => handlePopover()} />
        </Col>
      </Row>
      <Form
        form={form}
        style={{ display: "flex", flexDirection: "column", minWidth: "210px" }}
        name="basic"
        initialValues={{
          remember: true,
        }}
        onFinish={onFilterFinish}
        onFinishFailed={onFilterFailed}
        autoComplete="off"
      >
        <Form.Item name="contract">
          <Select
            style={{ width: "90%", margin: "5px" }}
            showSearch
            placeholder="Contratos"
            optionFilterProp="children"
            loading={allContracts?.length === 0}
          >
            {sortAlphaObj(allContracts).map((contract) => (
              <Option key={contract.id} value={contract.id}>
                {contract.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="wallet">
          <Select
            style={{ width: "90%", margin: "5px" }}
            showSearch
            placeholder="Carteira"
            optionFilterProp="children"
            loading={allWallets?.length === 0}
            filterOption={(input, option) =>
              option.props.children
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0 ||
              option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {allWallets.map((wallet) => (
              <Option key={wallet.name} value={wallet.name}>
                {wallet.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="date" rules={[{ required: true, message: "Prencha este campo." }]} >
          <DatePicker
            style={{ width: "90%", margin: "5px" }}
            picker="month"
            locale={locale}
            placeholder="Mês/Ano"
            format="MM/YYYY"
            disabledDate={(current) => {
              // Limitar seleção entre 2 anos atrás e o mês atual
              const twoYearsAgo = moment().subtract(2, 'years').startOf('month');
              const currentMonth = moment().endOf('month');
              return current && (current < twoYearsAgo || current > currentMonth);
            }}
          />
        </Form.Item>
        <Row justify="space-between" style={{ width: "92%" }}>
          <Button type="text" onClick={clear}>
            Limpar
          </Button>
          <Button type="primary" htmlType="submit" className="filter-submit-btn">
            Aplicar
          </Button>
        </Row>
      </Form>
    </>
  );
}

export default BornupFilter;
