import {
  filterBySearch,
  filterContracts,
  filterByState,
} from "../../../controllers/clients/clientsContoller";
import { customers, contracts } from "../../../__mocks__/clients/data";

describe("filterBySearch", () => {
  it("should return empty array if data or search is not provided", () => {
    const data = [];
    const search = "";

    const result = filterBySearch(data, search);

    expect(result).toEqual([]);
  });
  it("should return filtered data if data and search is provided (searching by name)", () => {
    const data = customers;
    const search = "agro";
    const result = filterBySearch(data, search);
    const expectedResult = data.filter((client) =>
      JSON.stringify(client).toLowerCase().includes(search.toLowerCase())
    );
    expect(result).toEqual(expectedResult);
  });
  it("should return filtered data if data and search is provided (searching by cnpj)", () => {
    const data = customers;
    const search = "11.111.111/11111-11";
    const result = filterBySearch(data, search);
    const expectedResult = data.filter((client) =>
      JSON.stringify(client).toLowerCase().includes(search.toLowerCase())
    );
    expect(result).toEqual(expectedResult);
  });

  it("should return filtered data if data and search is provided (searching by itsm_id)", () => {
    const data = customers;
    const search = "11111";
    const result = filterBySearch(data, search);
    const expectedResult = data.filter((client) =>
      JSON.stringify(client).toLowerCase().includes(search.toLowerCase())
    );
    expect(result).toEqual(expectedResult);
  });

  it("should return filtered data if data and search is provided (searching by crm_id)", () => {
    const data = customers;
    const search = "6723874528374592367435780934787623";
    const result = filterBySearch(data, search);
    const expectedResult = data.filter((client) =>
      JSON.stringify(client).toLowerCase().includes(search.toLowerCase())
    );
    expect(result).toEqual(expectedResult);
  });

  it("should return filtered data if data and search is provided (searching by account_id)", () => {
    const data = customers;
    const search = "***************";
    const result = filterBySearch(data, search);
    const expectedResult = data.filter((client) =>
      JSON.stringify(client).toLowerCase().includes(search.toLowerCase())
    );
    expect(result).toEqual(expectedResult);
  });

  it("should return an empty array if no data was found", () => {
    const data = customers;
    const search = "not found";
    const result = filterBySearch(data, search);
    expect(result).toEqual([]);
  });

  it("should return an empty array if no data nor search is found", () => {
    const result = filterBySearch();
    expect(result).toEqual([]);
  });

  it("should return an empty array if no data is undefined", () => {
    const result = filterBySearch(undefined, undefined);
    expect(result).toEqual([]);
  });
  it("should return an empty array if no data is null", () => {
    const result = filterBySearch(null, null);
    expect(result).toEqual([]);
  });
});

describe("filterContracts", () => {
  it("should return an empty array if no contracts was found", () => {
    const result = filterContracts();
    expect(result).toEqual([]);
  });

  it("should return an empty array if no contracts was found", () => {
    let data = contracts;
    let customer = "not found";
    let state = "all";
    const result = filterContracts(data, customer, state);
    expect(result).toEqual([]);
  });

  it("should return an empty array if customer is undefined", () => {
    let data = contracts;
    let customer = undefined;
    let state = "all";
    const result = filterContracts(data, customer, state);
    expect(result).toEqual([]);
  });

  it("should filter data based on the active state (ativosA)", () => {
    let data = contracts;
    let customer = customers[0]; 
    let state = "ativosA";
    const result = filterContracts(data, customer, state);

    // Deve retornar apenas contratos ativos de clientes que têm pelo menos um contrato ativo
    const expectedResult = data.filter(
      (contract) =>
        (contract.customer?.id === customer.id ||
         contract.customer?.itsm_id === customer.identifications?.itsm_id) &&
        contract.active === 1 &&
        // Cliente deve ter pelo menos um contrato ativo
        data.some(c =>
          (c.customer?.id === customer.id ||
           c.customer?.itsm_id === customer.identifications?.itsm_id) &&
          c.active === 1
        )
    );
    expect(result).toEqual(expectedResult);
  });
  it("should filter data based on the active state (ativosI)", () => {
    let data = contracts;
    let customer = customers[2]; // customer3 - tem apenas contratos inativos
    let state = "ativosI";
    const result = filterContracts(data, customer, state);

    // Deve retornar contratos inativos apenas de clientes que têm TODOS os contratos inativos
    const expectedResult = data.filter(
      (contract) =>
        (contract.customer?.id === customer.id ||
         contract.customer?.itsm_id === customer.identifications?.itsm_id) &&
        contract.active === 0 &&
        // Cliente deve ter TODOS os contratos inativos
        data.filter(c =>
          c.customer?.id === customer.id ||
          c.customer?.itsm_id === customer.identifications?.itsm_id
        ).every(c => c.active === 0)
    );
    expect(result).toEqual(expectedResult);
  });

  it("should NOT return inactive contracts for clients with active contracts (ativosA)", () => {
    let data = contracts;
    let customer = customers[1]; // customer2 - tem 1 ativo + 1 inativo
    let state = "ativosA";
    const result = filterContracts(data, customer, state);

    // Deve retornar apenas o contrato ativo, não o inativo
    expect(result).toHaveLength(1);
    expect(result[0].active).toBe(1);
    expect(result[0].name).toBe("Contrato ativo 2");
  });

  it("should NOT return any contracts for clients with only inactive contracts (ativosA)", () => {
    let data = contracts;
    let customer = customers[2]; // customer3 - tem apenas contratos inativos
    let state = "ativosA";
    const result = filterContracts(data, customer, state);

    // Não deve retornar nenhum contrato
    expect(result).toHaveLength(0);
  });
  it("should return all data if state is all", () => {
    let data = contracts;
    let customer = "customer3";
    let state = "all";
    const result = filterContracts(data, customer, state);
    const expectedResult = data.filter((contract) => contract.id === customer);
    expect(result).toEqual(expectedResult);
  });
});

describe("filterByState", () => {
  it("should return an empty array if no data was found", () => {
    const result = filterByState();
    expect(result).toEqual([]);
  });
  it("should filter data based on the active state (ativosA)", () => {
    let data = customers;
    let state = "ativosA";

    const result = filterByState(data, state, contracts);
    const expectedResult = data.filter(
      (customer) =>
        customer.id === "customer1" &&
        filterContracts(contracts, customer.id, state)?.length
    );
    expect(result).toEqual(expectedResult);
  });
  it("should filter data based on the active state (ativosI)", () => {
    let data = customers;
    let state = "ativosI";
    const result = filterByState(data, state, contracts);
    const expectedResult = data.filter(
      (customer) =>
        customer.id === "customer3" &&
        filterContracts(contracts, customer.id, state)?.length
    );
    expect(result).toEqual(expectedResult);
  });
  it("should filter data based on the active state (inativosC)", () => {
    let data = customers;
    let state = "inativosC";
    const result = filterByState(data, state, contracts);
    const expectedResult = data.filter(
      (customer) =>
        customer.id === "customer2" &&
        filterContracts(contracts, customer.id, state)?.length
    );
    expect(result).toEqual(expectedResult);
  });
});
