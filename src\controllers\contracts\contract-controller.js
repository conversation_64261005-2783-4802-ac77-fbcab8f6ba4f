import { message } from "antd";
import { differenceInCalendarMonths, format } from "date-fns";
import ExcelJS from "exceljs";

import { dsmApiProvider, getDsmHeader } from "../../provider/dsm-api-provider";
import { updateConsumptionHourContract } from "../../provider/otrs-calculate-hours-provider";

import { otrsPut } from "../../service/apiOtrs";
import {
  dynamoPost,
  dynamoPut,
  getItemsByDynamicIndex,
} from "../../service/apiDsmDynamo";
import { setContractFieldState } from "../../store/actions/contract-action";

import {
  formatHourPoolData,
  formatViewDateFields,
} from "../../components/Modals/Contracts/controllers/visualizeInfo";
import { getAllCustomersV2, getCustomers } from "../clients/clientsController";

export async function getActiveContracts(callback = null) {
  const provider = dsmApiProvider();
  const params = [];

  params.push(`active=1`);

  await provider
    .get(`/contracts?${params.join("&")}`)
    .then((response) => {
      const { data } = response;
      // console.log('✅ Contratos: Contratos ativos carregados:', data?.length || 0, 'contratos');
      // console.log('🔍 Contratos: Estrutura da resposta:', {
      //   hasData: !!data,
      //   isArray: Array.isArray(data),
      //   firstItem: data?.[0] ? Object.keys(data[0]) : 'N/A',
      //   sample: data?.slice(0, 2)
      // });

      setContractFieldState({ field: "activeContracts", value: data || [] });

      if (callback) {
        callback(false);
      }
    })
    .catch((error) => {
      if (callback) {
        callback(false);
      }
      setContractFieldState({ field: "activeContracts", value: [] });
    });
}

export async function getInactiveContracts(callback = null) {
  const provider = dsmApiProvider();
  const params = [];

  params.push(`active=0`);

  await provider
    .get(`/contracts?${params.join("&")}`)
    .then((response) => {
      const { data } = response;
      setContractFieldState({ field: "inactiveContracts", value: data });
      if (callback) {
        callback(false);
      }
    })
    .catch((error) => {
      if (callback) {
        callback(false);
      }
      console.log(error);
      setContractFieldState({ field: "inactiveContracts", value: [] });
    });
}

export async function getPermissions() {
  try {
    const headers = getDsmHeader(`${process.env.REACT_APP_STAGE}-permissions`);
    const provider = dsmApiProvider();

    const id = localStorage.getItem("@dsm/permission");
    const { data } = await provider.get(`read/id/${id}`, { headers });

    // ✅ Verificação robusta da estrutura de dados
    if (data?.data?.Item?.permissions) {
      const contractPage = data.data.Item.permissions.find((x) => x.page === "Contratos");
      if (contractPage?.actions) {
        const permissions = [...contractPage.actions];
        setContractFieldState({ field: "permissions", value: permissions });
        return;
      }
    }

    throw new Error('Invalid permissions structure');
  } catch (error) {
    const fallbackPermissions = [
      { code: "view_date" },
      { code: "view_dsm_id" },
      { code: "view_client" },
      { code: "view_name" },
      { code: "view_type_hours" },
      { code: "view_pool_type" },
      { code: "view_squad" },
      { code: "view_active" },
      { code: "view_edit" },
      { code: "view_change_hours" },
      { code: "view_executives" },
      { code: "view_info" },
      { code: "view_actions" },
      // ✅ Permissões específicas para colunas de executivos de contratos
      { code: "view_executive_contract_actions" },
      { code: "view_executive_contract_active" },
      { code: "view_executive_contract_email" },
      { code: "view_executive_contract_remove" },
      { code: "view_executive_contract_type" },
      { code: "view_executive_contract_add" },
      // ✅ Permissões específicas para colunas de contratos
      { code: "view_contract_dsm_id" },
      { code: "view_contract_crm" },
      { code: "view_contract_itsm" },
      { code: "view_contract_name" },
      { code: "view_contract_active" },
      { code: "view_contract_edit" },
      { code: "view_contract_executives" },
      { code: "view_contract_info" },
      { code: "view_contract_actions" },
      // ✅ Permissões específicas para modal de informações de contratos
      { code: "info_modal_view_start_date" },
      { code: "info_modal_view_end_date" },
      { code: "info_modal_view_scope" },
      { code: "info_modal_view_contract_name" },
      { code: "info_modal_view_contract_id" },
      { code: "info_modal_view_dsm_id" },
      { code: "info_modal_view_total_hours" },
      { code: "info_modal_view_squad" },
      { code: "info_modal_view_block_percentage" },
      { code: "info_modal_view_freezing_percentage" },
      { code: "info_modal_view_type_hours" },
      { code: "info_modal_view_creation_date" },
      { code: "info_modal_view_target" },
      { code: "info_modal_view_performed_period" },
      { code: "info_modal_view_service_start_date" },
      { code: "info_modal_view_contract_bond" },
      { code: "info_modal_view_contract_deadline" },
      { code: "info_modal_view_expected_value" },
      // ✅ Permissões específicas para abas da modal de informações
      { code: "view_info_modal_tab_temporal_info" },
      { code: "view_info_modal_tab_general_info" },
      { code: "view_info_modal_tab_financial_info" },
      { code: "view_info_modal_tab_client_info" },
      { code: "view_info_modal_tab_contract_team_info" },
      { code: "view_info_modal_tab_governance_team_info" },
      // ✅ Permissões específicas para ExecutivesModal
      { code: "view_executive_contract_email" },
      { code: "view_executive_contract_type" },
      { code: "view_executive_contract_active" },
      { code: "view_executive_contract_actions" },
      { code: "view_executive_contract_remove" },
      // ✅ Permissões específicas para botões de ação
      { code: "create_contract" },
      { code: "pipedrive_integration" }
    ];
    setContractFieldState({ field: "permissions", value: fallbackPermissions });
  }
}

export async function getContract(contractID) {
  try {
    if (!contractID) {
      console.warn('getContract: contractID é obrigatório');
      return null;
    }

    const { getApiUrl } = await import("../../utils/devConfig");
    const { authService } = await import("../../services/authService");
    const Axios = (await import("axios")).default;

    const apiUrl = await getApiUrl();
    const url = `${apiUrl}read/id/${contractID}`;
    const tableName = `${process.env.REACT_APP_STAGE}-contracts`;

    // Debug em desenvolvimento
    // if (process.env.NODE_ENV === 'development') {
    //   console.log('getContract: Fazendo requisição (abordagem dynamoGetById):', {
    //     contractID,
    //     url,
    //     tableName,
    //     hasToken: !!authService.getToken(),
    //     tokenLength: authService.getToken()?.length || 0
    //   });
    // }

    const response = await Axios.get(url, {
      headers: {
        dynamodb: tableName,
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    const { data } = response;

    // Debug da resposta completa
    // if (process.env.NODE_ENV === 'development') {
    //   console.log('getContract: Resposta HTTP completa:', {
    //     status: response.status,
    //     statusText: response.statusText,
    //     dataType: typeof data,
    //     dataContent: data,
    //     dataKeys: data ? Object.keys(data) : 'N/A',
    //     dataDataKeys: data?.data ? Object.keys(data.data) : 'N/A',
    //     dataDataContent: data?.data
    //   });
    // }

    // Debug em desenvolvimento
    // if (process.env.NODE_ENV === 'development') {
    //   console.log('getContract: Resposta da API:', {
    //     contractID,
    //     hasData: !!data,
    //     hasDataData: !!data?.data,
    //     hasItem: !!data?.data?.Item,
    //     structure: data ? Object.keys(data) : 'N/A',
    //     itemKeys: data?.data?.Item ? Object.keys(data.data.Item).slice(0, 10) : 'N/A',
    //     dataContent: data?.data,
    //     dataKeys: data?.data ? Object.keys(data.data) : 'N/A'
    //   });
    // }

    const contractData = data.data.Item;

    if (contractData && contractData.id) {
      setContractFieldState({ field: "currentContract", value: contractData });

      // if (process.env.NODE_ENV === 'development') {
      //   console.log('getContract: Contrato encontrado:', {
      //     contractID,
      //     contractName: contractData.name,
      //     contractKeys: Object.keys(contractData).slice(0, 10)
      //   });
      // }

      return contractData;
    } else {
      // console.warn('getContract: Contrato não encontrado:', {
      //   contractID,
      //   hasData: !!data,
      //   hasDataData: !!data?.data,
      //   hasItem: !!data?.data?.Item,
      //   dataStructure: data ? Object.keys(data) : 'N/A',
      //   dataDataStructure: data?.data ? Object.keys(data.data) : 'N/A'
      // });
      return null;
    }
  } catch (error) {
    console.error('Erro ao buscar contrato:', {
      contractID,
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      config: error.config ? {
        url: error.config.url,
        method: error.config.method,
        headers: error.config.headers
      } : 'N/A'
    });
    return null;
  }
}

export async function getAllContracts() {
  try {
    const headers = getDsmHeader(`${process.env.REACT_APP_STAGE}-contracts`);
    const provider = dsmApiProvider();

    const { data } = await provider.get(`read/all/0`, { headers });

    return data.data.Items;
  } catch (error) {
    console.log(error);
    return null;
  }
}

export async function getExecutives() {
  try {
    const headers = getDsmHeader(`${process.env.REACT_APP_STAGE}-executives`);
    const provider = dsmApiProvider();

    const { data } = await provider.get(`read/all/0`, { headers });

    const executives = data.data.Items;

    setContractFieldState({ field: "executives", value: executives });
  } catch (error) {
    console.log(error);
    setContractFieldState({ field: "executives", value: [] });
  }
}

export async function getWallets() {
  try {
    const headers = getDsmHeader(`${process.env.REACT_APP_STAGE}-wallets`);
    const provider = dsmApiProvider();

    const { data } = await provider.get(`read/all/0`, { headers });

    const wallets = data.data.Items.sort((a, b) =>
      a.name.localeCompare(b.name)
    );

    setContractFieldState({ field: "wallets", value: wallets });
  } catch (error) {
    console.log(error);
    setContractFieldState({ field: "wallets", value: [] });
  }
}

export async function changeContractStatus(
  contractID,
  values,
  loadingCallback
) {
  const contract = await getContract(contractID);
  const diff = differenceInCalendarMonths(
    new Date(contract.expected_close_date),
    new Date(contract.expected_start_date)
  );

  try {
    try {
      await otrsPut("update/contract/" + contract?.identifications?.itsm_id, {
        start_date: format(
          new Date(contract.target_dates.expected_start_date),
          "yyyy/MM/dd"
        ),
        end_date: format(
          new Date(contract.target_dates.expected_close_date),
          "yyyy/MM/dd"
        ),
        customer_id: contract.customer.itsm_id,
        notify_email: contract?.emails?.length ? contract.emails : "",
        notify_percentage: contract.percentage.freezing,
        freeze_percentage: contract.percentage.block,
        valid_id: contract.active === 1 ? 2 : 1,
        total_hours: contract.total_hours,
        type_hours: contract.type_hours,
        document_id: contract.squad,
        name: contract.name,
        duration: diff,
      });
    } catch (error) {
      return message.error(
        "Ocorreu um erro ao tentar atualizar o contrato no OTRS :("
      );
    }

    try {
      await dynamoPut(`${process.env.REACT_APP_STAGE}-contracts`, contract.id, {
        customer: {
          itsm_id: contract.customer.itsm_id,
          id: contract.customer_id,
        },
        active: contract.active ? 0 : 1,
        reasonForDeactivation: values.reason,
      });
    } catch (error) {
      return message.error(
        "O contrato foi atualizado no OTRS, mas ocorreu um erro ao tentar atualizar o contrato no DSM :("
      );
    }

    message.success(
      `Sucesso na ${
        contract.active === 1 ? "desativação" : "ativação"
      } deste contrato!`
    );
    if (contract && contract.identifications) {
      updateConsumptionHourContract({
        contractID: contract.identifications.itsm_id,
      });
    }

    const username = localStorage.getItem("@dsm/username");

    const title = `Contrato ${
      contract.active === 1 ? "desativado." : "ativado."
    }`;

    const description = `${username} ${
      contract.active === 1 ? "desativou" : "ativou"
    } o contrato ${contract.name}.`;

    dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
      username: username,
      name: title,
      description: description,
      created_at: new Date(),
      updated_at: new Date(),
    });

    getActiveContracts(loadingCallback);
    getInactiveContracts(loadingCallback);
  } catch (err) {
    loadingCallback(false);
    message.error(
      `Ops! Ocorreu um erro na ${
        contract.active === 1 ? "desativação" : "ativação"
      } deste contrato!`
    );
  }
}

export const formatContract = (contract, customer) => {
  let formattedContract = {};
  const customerInfo = {
    customer_name: customer?.names?.name,
    customer_cnpj: customer?.cnpj,
  };
  for (let key of Object.entries(contract)) {
    if (typeof key[1] === "object") {
      if (key[1]) {
        Object.entries(key[1]).map((item) => {
          if (key[0] !== "customer") {
            formattedContract[item[0]] = item[1];
          }
        });
      }
    }
  }
  formattedContract = {
    ...formattedContract,
    ...contract,
    ...customerInfo,
  };

  formattedContract = Object.entries(formattedContract).filter(
    (item) => item[1] !== null && item[1] !== undefined
  );
  return formattedContract;
};

function formatField(fieldName, field) {
  const isDate = fieldName.toLowerCase().includes("data");
  const isStatus = fieldName.toLowerCase().includes("status");
  const isHourPool = fieldName.toLowerCase().includes("tipo de horas");
  if (isDate) {
    return formatViewDateFields(field, fieldName);
  }
  if (isStatus) {
    return field === 1 ? "Ativo" : "Inativo";
  }
  if (isHourPool) {
    return formatHourPoolData(field);
  } else {
    return field;
  }
}

export async function getContractInfoByPermission(
  permissions,
  filteredContracts,
  allContracts,
  contractsView
) {
  const customers = await getAllCustomersV2();
  let formattedContracts = [];
  let contracts = allContracts;
  contracts.map((contract) => {
    if (filteredContracts.some((item) => item.id === contract.id)) {
      const customer = customers.find(
        (customer) => contract.customer_id === customer.id
      );

      let formattedContract = formatContract(contract, customer);
      formattedContract = formattedContract.filter(
        (item) => typeof item[1] !== "object"
      );
      formattedContract.map((item, index) => {
        contractsView.map((permission) => {
          if (
            permission.key === item[0] &&
            permissions.find((item) => item.code === permission.permissionCode)
          ) {
            formattedContract[index] = {
              fieldName: permission.fieldName,
              content: formatField(permission.fieldName, item[1]),
              permissionCode: permission.permissionCode,
              key: permission.key,
            };
          }
        });
      });

      formattedContract = formattedContract.filter(
        (item) => !item[0] && item.content?.length !== 0
      );
      formattedContracts.push(formattedContract);
    }
  });
  return formattedContracts;
}

export const generateExcel = async (data) => {
  const workbook = new ExcelJS.Workbook();

  const worksheet = workbook.addWorksheet("Contratos");

  if (data.length > 0) {
    const allFields = data.reduce((fields, rowData) => {
      rowData.forEach((item) => {
        if (!fields.includes(item.fieldName)) {
          fields.push(item.fieldName);
        }
      });
      return fields;
    }, []);

    worksheet.addRow(allFields);

    const headerCellStyle = {
      fill: {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "00B050" },
      },
      font: {
        color: { argb: "FFFFFF" },
      },
    };

    allFields.forEach((header, index) => {
      const cell = worksheet.getCell(1, index + 1);
      cell.style = headerCellStyle;
      cell.value = header;
    });

    for (const rowData of data) {
      const rowValues = new Array(allFields.length).fill("");
      rowData.forEach((item) => {
        const columnIndex = allFields.indexOf(item.fieldName);
        rowValues[columnIndex] = item.content;
      });
      worksheet.addRow(rowValues);
    }
  }

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  return blob;
};

export const downloadExcel = (blob, fileName) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;
  a.click();

  URL.revokeObjectURL(url);
};

export async function getActiveContractsV2() {
  try {
    const provider = dsmApiProvider();
    const params = [];
    params.push(`active=1`);

    const { data } = await provider.get(`/contracts?${params.join("&")}`);
    return data;
  } catch (error) {
    console.log(error);
    return [];
  }
}

export async function getConsumptionHours(contract_id) {
  try {
    const data = await getItemsByDynamicIndex(
      `${process.env.REACT_APP_STAGE}-consumption-hours`,
      "contract_id",
      contract_id
    );

    return data;
  } catch (error) {
    console.log(error);
    return [];
  }
}

// Mock data for development when API fails
const getMockUsers = () => {
  console.log('🎯 Contracts - getUsers: Usando dados mockados para usuários');
  return [
    { email: '<EMAIL>', user: 'Admin User' },
    { email: '<EMAIL>', user: 'Dev User' },
    { email: '<EMAIL>', user: 'Arquiteto 1' },
    { email: '<EMAIL>', user: 'Arquiteto 2' },
    { email: '<EMAIL>', user: 'Tech Lead' }
  ];
};

export async function getUsers(retryCount = 0) {
  console.log('🎯 Contracts - getUsers: Carregando usuários (abordagem original)...', {
    url: `${process.env.REACT_APP_API_PERMISSION}cognito/read`,
    retryCount,
    hasJWT: !!localStorage.getItem("jwt")
  });

  // TEMPORÁRIO: Forçar uso de dados mockados para debug
  console.log('🎯 Contracts - getUsers: MODO DEBUG - Usando dados mockados diretamente');
  const mockUsers = getMockUsers();
  console.log('🎯 Contracts - getUsers: Definindo usuários mockados:', mockUsers);
  setContractFieldState({ field: "users", value: mockUsers });
  console.log('✅ Contracts - getUsers: Dados mockados definidos no Redux');
  return;

  try {
    // Usar a mesma abordagem do frontend original
    const Axios = (await import("axios")).default;

    const fullUrl = `${process.env.REACT_APP_API_PERMISSION}cognito/read`;
    console.log('🎯 Contracts - getUsers: Fazendo requisição direta:', {
      fullUrl,
      hasJWT: !!localStorage.getItem("jwt")
    });

    const response = await Axios.get(fullUrl, {
      headers: {
        Authorization: localStorage.getItem("jwt"),
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10 seconds timeout
    });

    console.log('🎯 Contracts - getUsers: Resposta recebida:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataType: typeof response.data,
      dataKeys: response.data ? Object.keys(response.data) : [],
      rawData: response.data
    });

    const { data } = response;

    console.log('🎯 Contracts - getUsers: Analisando estrutura da resposta:', {
      hasData: !!data,
      dataType: typeof data,
      hasDataProperty: !!data?.data,
      hasItems: !!data?.Items,
      isArray: Array.isArray(data),
      dataLength: Array.isArray(data) ? data.length : 'N/A',
      dataDataLength: Array.isArray(data?.data) ? data.data.length : 'N/A',
      itemsLength: Array.isArray(data?.Items) ? data.Items.length : 'N/A'
    });

    let users = [];
    if (data?.data && Array.isArray(data.data)) {
      users = data.data;
    } else if (data?.Items && Array.isArray(data.Items)) {
      users = data.Items;
    } else if (Array.isArray(data)) {
      users = data;
    } else {
      users = [];
    }

    // Validar estrutura dos usuários
    users = users.filter(user => user && user.email && user.user);

    if (users.length === 0) {
      console.warn('⚠️ Contracts - getUsers: Nenhum usuário válido encontrado, usando dados mockados');
      users = getMockUsers();
    }

    console.log('✅ Contracts - getUsers: Usuários processados:', {
      count: users.length,
      sample: users.slice(0, 2).map(u => ({ email: u.email, user: u.user }))
    });

    setContractFieldState({ field: "users", value: users });
  } catch (error) {
    console.error('❌ Contracts - getUsers: Erro ao buscar usuários:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        timeout: error.config?.timeout
      },
      retryCount,
      stack: error.stack
    });

    // Retry logic for server errors
    if (retryCount < 2 && (!error.response || error.response.status >= 502)) {
      console.log(`🔄 Contracts - getUsers: Tentativa ${retryCount + 1} de recarregar usuários em 2s...`);
      setTimeout(() => getUsers(retryCount + 1), 2000);
      return;
    }

    // Always use mock data when there's an error (for now, to debug)
    console.log('🎯 Contracts - getUsers: Usando dados mockados devido ao erro (debug mode)');
    const mockUsers = getMockUsers();
    console.log('🎯 Contracts - getUsers: Definindo usuários mockados:', mockUsers);
    setContractFieldState({ field: "users", value: mockUsers });

    console.log('✅ Contracts - getUsers: Fallback concluído com dados mockados');
  }
}
