import { dynamoGetById, dynamoPut } from "../../../service/apiDsmDynamo";
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
  Space,
  Tabs,
} from "antd";
import { EditOutlined } from "@ant-design/icons";
import React, { useState } from "react";
import { logBillingEdit } from "../../../controllers/audit/logBillingEdit";
import { initPage } from "../../../pages/Billing/controllers/complianceControllers";
import { moneyMask } from "../../../utils/money-maks";
import { LoadingOutlined } from "@ant-design/icons";

export const EditBilling = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loadingContract, setLoadingContracts] = useState(false);
  const [loading, setLoading] = useState(false);
  const [customerData, setCustomerData] = useState(null);
  const { billing, contracts, clients } = props;

  const [form] = Form.useForm();
  if (!contracts && !loadingContract) {
    return setLoadingContracts(true);
  }

  if (contracts && loadingContract) {
    return setLoadingContracts(false);
  }

  const handleSubmit = async ({ contract, percentage, taxes, led_support }) => {
    const { customer_email, payment_date, other_info, payment_document } =
      form.getFieldsValue();
    setLoading(true);
    const { contract_id, customer_id } = JSON.parse(contract.value);

    percentage = percentage + "%";
    taxes = taxes + "%";

    let created_at = new Date(),
      updated_at = new Date();

    try {
      const { payment_percent } = clients.find((c) => c.id === customer_id);
      const updatedPaymentData = payment_percent.filter(
        (p) => p.contract_id !== contract_id
      );

      dynamoPut(`${process.env.REACT_APP_STAGE}-customers`, customer_id, {
        payment_percent: [
          ...updatedPaymentData,
          {
            customer_id,
            contract_id,
            percentage,
            created_at,
            updated_at,
            taxes,
            led_support,
          },
        ],
        billing_info: {
          payment_date,
          customer_email,
          other_info,
          payment_document,
        },
      });
      logBillingEdit(contract_id, customer_id);

      await initPage([]);
      message.success("Billing atualizado com sucesso!");
      setLoading(false);
      handleCancel();
    } catch (error) {
      console.log(error);
      message.error("Ocorreu um erro ao tentar editar o billing.");
      setLoading(false);
    }
  };

  const showModal = async () => {
    setIsModalVisible(true);
    setLoading(true);
    const customer = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-customers`,
      billing.customer_id
    );
    const name = contracts?.find(
      (e) =>
        e.active === 1 &&
        e.name.toLowerCase().includes("billing") &&
        e.id === billing.contract_id
    ).name;
    form.setFieldsValue({
      contract: {
        label: name,
        value: JSON.stringify({
          contract_id: billing.contract_id,
          customer_id: billing.customer_id,
        }),
      },

      percentage: billing.percentage.replace("%", ""),
      taxes: billing.taxes?.replace("%", "") || 0,
      led_support: billing.led_support || "",
    });
    setCustomerData(customer);
    setLoading(false);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModalVisible(false);
  };

  const tabItems = [
    {
      key: "1",
      label: "Informação de Billing",
      children: (
        <EditBillingGeneralForm
          contracts={contracts}
          billing={billing}
          loadingContract={loadingContract}
        />
      ),
    },
    {
      key: "2",
      label: "Informação do Cliente",
      children: (
        <EditBillingCustomerForm customerData={customerData} form={form} />
      ),
    },
  ];

  return (
    <>
      <Button type="text" onClick={showModal}>
        <EditOutlined />
      </Button>
      <Modal
        title="Edite um Billing"
        open={isModalVisible}
        onOk={() => {
          handleOk();
        }}
        confirmLoading={loading}
        okText="Editar Billing"
        onCancel={handleCancel}
        cancelText="Cancelar"
      >
        {loading ? (
          <Row justify="center">
            <LoadingOutlined />
          </Row>
        ) : (
          <Form
            layout="vertical"
            onFinish={async () => {
              await handleSubmit({
                percentage: form.getFieldValue("percentage"),
                contract: form.getFieldValue("contract"),
                taxes: form.getFieldValue("taxes"),
                led_support: form.getFieldValue("led_support"),
              });
            }}
            requiredMark={false}
            form={form}
          >
            <Tabs defaultActiveKey="1" items={tabItems} />
          </Form>
        )}
      </Modal>
    </>
  );
};

const EditBillingGeneralForm = (props) => {
  const { contracts, billing, loadingContract } = props;
  const { Option } = Select;

  const handleLedSupportChange = (e) => {
    const value = e.target.value;
    const formattedValue = moneyMask(value);
    e.currentTarget.value = formattedValue ? `US$ ${formattedValue}` : "";
  };

  return (
    <>
      <Form.Item
        name="contract"
        label="Contrato"
        rules={[{ required: true, message: "Preencha este campo." }]}
      >
        <Select
          placeholder="Selecione um contrato"
          showSearch
          optionFilterProp="children"
          filterOption={(input, option) =>
            option.children?.toLowerCase().indexOf(input?.toLowerCase()) >= 0
          }
          loading={loadingContract}
        >
          {contracts
            ?.filter(
              (e) =>
                e.active === 1 &&
                e.name.toLowerCase().includes("billing") &&
                e.id === billing.contract_id
            )
            ?.map((e, index) => {
              return (
                <Option
                  key={index}
                  value={JSON.stringify({
                    contract_id: e.id,
                    customer_id: e?.customer.id,
                  })}
                >
                  {e?.name}
                </Option>
              );
            })}
        </Select>
      </Form.Item>
      <Form.Item
        rules={[{ required: true, message: "Preencha este campo." }]}
        label="Porcentagem de Desconto"
        name="percentage"
      >
        <Input placeholder="Porcentagem de Desconto" suffix="%" />
      </Form.Item>
      <Form.Item label={"Impostos"} name="taxes">
        <Input placeholder="Porcentagem de Impostos" type="number" suffix="%" />
      </Form.Item>
      <Form.Item label={"Led Support"} name="led_support">
        <Input
          placeholder="Led Support"
          onInput={handleLedSupportChange}
          type="text"
        />
      </Form.Item>
    </>
  );
};

const EditBillingCustomerForm = (props) => {
  const { customerData } = props;
  const { TextArea } = Input;

  return (
    <>
      <Space direction="vertical" style={{ width: "100%" }}>
        <Form.Item
          name="payment_document"
          label="CNPJ de faturamento"
          initialValue={
            customerData?.billing_info?.payment_document !== undefined
              ? customerData?.billing_info?.payment_document
              : customerData?.cnpj
          }
        >
          <TextArea
            rows={2}
            placeholder="CNPJ de faturamento"
            style={{ height: "auto", minHeight: "auto" }}
          />
        </Form.Item>
      </Space>
      <Space align="baseline" style={{ margin: "1em 0" }}>
        Pagamento deste cliente é feito no dia{" "}
        <Form.Item
          rules={[
            {
              pattern: /^(0?[1-9]|[12][0-9]|3[01])$/,
              message: "Dia inválido (1-31)",
            },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                const day = parseInt(value, 10);
                if (day >= 1 && day <= 31) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('Dia deve estar entre 1 e 31'));
              }
            }
          ]}
          initialValue={customerData?.billing_info?.payment_date}
          name="payment_date"
        >
          <Input
            placeholder="00"
            style={{ width: "60px", textAlign: "center" }}
          />
        </Form.Item>{" "}
        de cada mês.
      </Space>
      <Form.Item
        label="Email do Cliente"
        name="customer_email"
        rules={[{ type: "email", message: "Insira um email válido." }]}
        initialValue={customerData?.billing_info?.customer_email}
      >
        <Input placeholder="Email para envio do faturamento" />
      </Form.Item>
      <Form.Item
        label="Outras informações"
        name="other_info"
        initialValue={customerData?.billing_info?.other_info}
      >
        <TextArea
          rows={4}
          placeholder="Ex.: Faturamento para mais de uma conta"
          style={{ height: "auto", minHeight: "auto" }}
        />
      </Form.Item>
    </>
  );
};
