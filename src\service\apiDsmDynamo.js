import Axios from "axios";
import useS<PERSON> from "swr";
import * as proposalController from "../controllers/Proposals/proposal-controller";
import * as customerController from "../controllers/clients/clientsController";
import { uniqByKeepLast } from "../utils/uniqueByKeepLast";
import { formatTableData } from "../controllers/switchRoles/switch-role-controller";
import { dsmApiProvider, dsmApiProviderDynamic } from "../provider/dsm-api-provider";
import { switchRoleProvider } from "../provider/switch-role-api-provider";
import { format } from "date-fns";
import { authService } from "../services/authService";
import { getApiUrl } from "../utils/devConfig";

const requestCache = new Map();
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutos para dados dinâmicos

const getCacheKey = (tableName, id) => `${tableName}:${id}`;

const getCachedData = (key) => {
  const cached = requestCache.get(key);
  if (!cached) return null;

  const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;
  if (isExpired) {
    requestCache.delete(key);
    return null;
  }

  return cached.data;
};

const setCachedData = (key, data) => {
  requestCache.set(key, {
    data,
    timestamp: Date.now()
  });
};

const dsmProvider = dsmApiProvider();
const switchRoleApiProvider = switchRoleProvider();

export const dynamoSwitchRoleAccessesPerUser = async (body) => {
  try {
    const currentUser = authService.getUserInfo().name;
    const apiUrl = await getApiUrl();
    const url = `${apiUrl}read/switch-role-exists/${currentUser}/all`;



    const { data } = await Axios.get(url, {
      headers: {
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    return data.data.Items;
  } catch (error) {

    throw error;
  }
};

export const dynamoGetById = async (tableName, id) => {
  if (!id || id === 'null' || id === 'undefined') {
    console.warn('⚠️ dynamoGetById: ID inválido fornecido:', id);
    throw new Error('ID é obrigatório e deve ser válido');
  }

  if (!tableName) {
    console.warn('⚠️ dynamoGetById: tableName é obrigatório');
    throw new Error('tableName é obrigatório');
  }

  const cacheKey = getCacheKey(tableName, id);
  const cachedData = getCachedData(cacheKey);

  if (cachedData) {
    return cachedData;
  }

  try {
    // Usar axios com baseURL já configurado
    const { data } = await Axios.get(`read/id/${id}`, {
      headers: {
        dynamodb: tableName,
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    // ramos

    const result = data.data?.Item || data.data || null;

    // ✅ Armazenar no cache
    setCachedData(cacheKey, result);

    return result;
  } catch (error) {
    console.error('❌ dynamoGetById: Erro na requisição:', {
      tableName,
      id,
      error: error.message
    });
    throw error;
  }
};

export function useDynamoGet(tableName) {
  const { data, error, mutate } = useSWR(tableName, async () => {
    if (tableName.endsWith("-customers")) {
      const list = await customerController.getAllCustomers();
      return list.Items;
    }

    const apiUrl = await getApiUrl();
    const url = `${apiUrl}read/all/0`;

    const response = await Axios.get(url, {
      headers: {
        dynamodb: tableName,
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    return response.data?.data?.Items || response.data?.Items || response.data;
  });

  return { data, error, mutate };
}

export function useDynamoGetCustomers(tableName, route, queryParams) {
  const { data, error, mutate } = useSWR(tableName, async () => {
    try {
      const apiUrl = await getApiUrl();
      const url = `${apiUrl}${route}?status=1`;

      const response = await Axios.get(url, {
        headers: {
          dynamodb: tableName,
          'Authorization': `Bearer ${authService.getToken()}`,
          'Content-Type': 'application/json'
        },
      });

      return response.data?.data?.Items || response.data?.Items || response.data;
    } catch (error) {
      console.error('❌ useDynamoGetCustomers: Erro na requisição:', error.message);
      throw error;
    }
  });

  return { data, error, mutate };
}

export function useDynamoGetQuery(tableName) {
  const { data, error, mutate } = useSWR(tableName, async () => {
    const response = await Axios.get(
      `${process.env.REACT_APP_API_PERMISSION}/read/all/0`,
      {
        headers: {
          dynamodb: tableName,
          'Authorization': `Bearer ${authService.getToken()}`,
          'Content-Type': 'application/json'
        },
      }
    );

    return response.data?.data?.Items || response.data?.Items || response.data;
  });

  return { data, error, mutate };
}

export const dolarGet = async (month, year) => {
  try {
    const apiUrl = await getApiUrl();
    const url = `${apiUrl}read/dolar/${month}/${year}`;

    const { data } = await Axios.get(url, {
      headers: {
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    return data.data;
  } catch (error) {
    console.error('❌ dolarGet: Erro na requisição:', error.message);
    throw error;
  }
};

export const genericPost = async (route, body) => {
  try {
    const apiUrl = await getApiUrl();
    const url = `${apiUrl}${route}`;

    const response = await Axios.post(url, { ...body }, {
      headers: {
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    return response.data?.data?.Items || response.data?.Items || response.data;
  } catch (error) {
    console.error('❌ genericPost: Erro na requisição:', error.message);
    throw error;
  }
};

export const dynamoGet = async (tableName) => {
  try {
    if (!tableName) {
      console.warn('⚠️ dynamoGet: tableName é obrigatório');
      throw new Error('tableName é obrigatório');
    }

    if (tableName.endsWith("-customers")) {
      const list = await customerController.getAllCustomers();
      return list.Items;
    }

    if (tableName.endsWith("-proposals")) {
      const list = await proposalController.getAllProposals();
      return list.Items;
    }

    // Usar axios com baseURL já configurado
    const response = await Axios.get(`read/all/0`, {
      headers: {
        dynamodb: tableName,
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });
    return response.data?.data?.Items || response.data?.Items || response.data;
  } catch (e) {
    console.error('❌ dynamoGet: Erro na requisição:', {
      tableName,
      error: e.message
    });
    return [];
  }
};

export const dynamoPost = async (tableName, body) => {
  try {
    if (!tableName) {
      console.warn('⚠️ dynamoCreate: tableName é obrigatório');
      throw new Error('tableName é obrigatório');
    }

    if (!body) {
      console.warn('⚠️ dynamoCreate: body é obrigatório');
      throw new Error('body é obrigatório');
    }

    if (!tableName === `${process.env.REACT_APP_STAGE}-tickets`) {
      delete body.id;
    }

    const apiUrl = await getApiUrl();
    const url = `${apiUrl}create`;

    const { data } = await Axios.post(url, { ...body }, {
      headers: {
        dynamodb: tableName,
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    return data;
  } catch (error) {
    console.error('❌ dynamoCreate: Erro na requisição:', {
      tableName,
      error: error.message
    });
    throw error;
  }
};

export const dynamoPut = async (tableName, id, body) => {
  try {
    if (!tableName) {
      console.warn('⚠️ dynamoPut: tableName é obrigatório');
      throw new Error('tableName é obrigatório');
    }

    if (!id || id === 'null' || id === undefined) {
      console.warn('⚠️ dynamoPut: ID inválido fornecido:', id);
      throw new Error('ID é obrigatório e deve ser válido');
    }

    // Permitir string "undefined" para compatibilidade com dados existentes

    if (!body) {
      console.warn('⚠️ dynamoPut: body é obrigatório');
      throw new Error('body é obrigatório');
    }

    let obj = {};

    Object.entries(body).forEach((o) => {
      if (o[0] !== "id" && o[1] !== undefined && o[1] !== null) {
        obj[o[0]] = { Action: "PUT", Value: o[1] };
      }
    });

    const apiUrl = await getApiUrl();
    const url = `${apiUrl}update/${id}`;

    const { data } = await Axios.put(url, { ...obj }, {
      headers: {
        dynamodb: tableName,
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    return data;
  } catch (error) {
    console.error('❌ dynamoPut: Erro na requisição:', {
      tableName,
      id,
      error: error.message
    });
    throw error;
  }
};

export const dynamoDelete = async (tableName, id) => {
  try {
    if (!tableName) {
      console.warn('⚠️ dynamoDelete: tableName é obrigatório');
      throw new Error('tableName é obrigatório');
    }

    if (!id || id === 'null' || id === 'undefined') {
      console.warn('⚠️ dynamoDelete: ID inválido fornecido:', id);
      throw new Error('ID é obrigatório e deve ser válido');
    }

    const apiUrl = await getApiUrl();
    const url = `${apiUrl}delete/${id}`;

    const { data } = await Axios.delete(url, {
      headers: {
        dynamodb: tableName,
        'Authorization': `Bearer ${authService.getToken()}`,
        'Content-Type': 'application/json'
      },
    });

    return data;
  } catch (error) {
    console.error('❌ dynamoDelete: Erro na requisição:', {
      tableName,
      id,
      error: error.message
    });
    throw error;
  }
};

export const dynamoGetSwitchRoleSolicitations = async () => {
  let solicitations = [];
  try {
    const dynamicProvider = await dsmApiProviderDynamic();

    let response = await dynamicProvider.get("read/switch-role");

    let Items = response.data?.data?.Items || response.data?.Items || [];
    let ssoReadResponse = await switchRoleApiProvider.post("sso-read", { id: 0 });

    let ssoReadData = ssoReadResponse.data?.body?.Items || ssoReadResponse.data?.Items || [];

    solicitations = formatTableData(Items);

    solicitations.forEach((e) => {
      for (let account of ssoReadData) {
        if (e.username === account.user) {
          e.arn = account.arn;
        }
      }
    });

    let tkt = [];
    let objectParatemers = [
      "username",
      "solicitations",
      "client_ticket",
      "client_name",
      "allowed",
    ];
    const newArr = uniqByKeepLast(solicitations, (i) => i.client_user);

    newArr.forEach((e) => {
      let obj = {};
      objectParatemers.forEach((parameter) => {
        if (parameter === "allowed")
          return (obj[parameter] = solicitations
            .filter((i) => i.client_user === e[0])
            .every((v) => v.allowed));
        if (parameter === "solicitations")
          return (obj[parameter] = solicitations.filter(
            (i) => i.client_user === e[0]
          ));
        return (obj[parameter] = solicitations.filter(
          (i) => i.client_user === e[0]
        )[0]?.[parameter]);
      });

      return tkt.push(obj);
    });
    return tkt;
  } catch (err) {
    throw err;
  }
};

export const dynamoGetPermissionSets = async () => {
  let arr = [];

  let { data } = await switchRoleApiProvider.post("sso-read", { id: 0 });

  data.body.Items.forEach((user) => {
    arr.push({
      linked: user.linked,
      user: user.user,
      arn: user.arn,
      id: user.id,
    });
  });
  return arr;
};

export const invokeStateMachine = async (body) => {
  const dsmProviderIntance = dsmApiProvider();
  try {
    await dsmProviderIntance.post("state-machine", body);
  } catch (e) {
    throw e;
  }
};

export const checkDaredeFullRole = async (account_id) => {
  try {
    const response = await dsmProvider.post("check-darede-full-role", {
      account: account_id,
    });

    if (response.status === 200 && response.data.success) {
      return response.data;
    } else {
      throw new Error(
        "A role darede-full NÃO existe ou não pode ser assumida."
      );
    }
  } catch (e) {
    throw e;
  }
};

export const sendWebhookNotification = (accountID, client, data) => {
  const body = {
    body: {
      "@type": "MessageCard",
      "@context": "https://schema.org/extensions",
      summary: "Issue *********",
      themeColor: "0d9347",
      title: "Solicitação de Switch Role",
      sections: [
        {
          activityTitle: authService.getUserInfo().name,
          activitySubtitle: format(new Date(), "dd/MM/yyyy, HH:mm"),
          activityImage:
            "https://cdn.iconscout.com/icon/free/png-256/switch-1470433-1244947.png",
          text: `Estou solicitando acesso para a(s) conta(s) '${accountID.join(
            ", "
          )}' do cliente ${
            client?.names?.fantasy_name
          }, tratando-se do ticket ${data.ticket_id}.`,
        },
      ],
    },
  };
  try {
    dsmProvider.post("send/notification", body);
  } catch (e) {
    // Silently fail for notifications
  }
};

export const createContract = async (body) => {
  const provider = dsmApiProvider();
  try {
    const response = await provider.post("contracts/create", body);
    return response.data;
  } catch (e) {
    throw e;
  }
};

export const getItemsByDynamicIndex = async (tableName, index, indexValue) => {
  const provider = dsmApiProvider();
  const headers = {
    dynamodb: tableName,
  };

  const queryParams = {
    indexName: index,
  };

  try {
    const response = await provider.get(
      `read/indexKey/${indexValue}?indexName=${index}`,
      {
        headers: headers,
      }
    );

    return response.data?.data?.Items || response.data?.Items || response.data;
  } catch (e) {
    return [];
  }
};
