import React from "react";
import "dayjs/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

import { Col, DatePicker, Typography } from "antd";
import { differenceInYears } from "date-fns";
import dayjs from "dayjs";

type DateFilterProps = {
  label: string;
  onChange: (value: dayjs.Dayjs | null) => void;
  defaultValue?: dayjs.Dayjs;
  value?: dayjs.Dayjs | null;
  limit?: boolean;
};

export const DateFilter = ({
  label,
  onChange,
  defaultValue,
  value,
  limit,
}: DateFilterProps) => {
  return (
    <>
      <Typography.Text>{label}</Typography.Text>
      <DatePicker
        placeholder="Selecione um mês"
        picker="month"
        style={{ width: "100%" }}
        locale={locale}
        onChange={(value) => onChange(value)}
        value={value !== undefined ? value : undefined}
        defaultValue={value === undefined ? defaultValue : undefined}
        defaultPickerValue={dayjs()}
        disabledDate={
          limit
            ? (current) => {
                return (
                  differenceInYears(new Date(), current.toDate()) < 13 ||
                  current > dayjs()
                );
              }
            : undefined
        }
      />
    </>
  );
};
