/**
 * Utilitário para gerenciar cache de filtros de data
 * Resolve problemas de cache que requerem CTRL+SHIFT+R
 */

import { useState, useEffect } from 'react';

/**
 * Chaves de cache relacionadas a filtros de data
 */
const DATE_FILTER_CACHE_KEYS = [
  'persist:customers',
  'persist:root',
  'dsm_cache_clients_date_filter',
  'dsm_session_clients_date_filter',
  'dsm_cache_date_filters',
  'antd_date_picker_cache'
];

/**
 * Limpa cache específico de filtros de data
 */
export const clearDateFilterCache = () => {
  console.log('🧹 Limpando cache de filtros de data...');
  
  DATE_FILTER_CACHE_KEYS.forEach(key => {
    try {
      // Limpar localStorage
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        console.log(`✅ Removido do localStorage: ${key}`);
      }
      
      // Limpar sessionStorage
      if (sessionStorage.getItem(key)) {
        sessionStorage.removeItem(key);
        console.log(`✅ Removido do sessionStorage: ${key}`);
      }
    } catch (error) {
      console.warn(`⚠️ Erro ao limpar cache ${key}:`, error);
    }
  });
  
  // Limpar cache específico do Redux persist para customers
  try {
    const persistRoot = localStorage.getItem('persist:root');
    if (persistRoot) {
      const parsed = JSON.parse(persistRoot);
      if (parsed.customers) {
        const customers = JSON.parse(parsed.customers);
        
        // Verificar se há objetos moment/dayjs corrompidos
        if (
          (customers.dtStart && typeof customers.dtStart === 'object' && customers.dtStart._isAMomentObject) ||
          (customers.dtEnd && typeof customers.dtEnd === 'object' && customers.dtEnd._isAMomentObject)
        ) {
          // Limpar apenas as datas corrompidas
          customers.dtStart = null;
          customers.dtEnd = null;
          
          parsed.customers = JSON.stringify(customers);
          localStorage.setItem('persist:root', JSON.stringify(parsed));
          
          console.log('✅ Cache corrompido de datas corrigido no Redux persist');
        }
      }
    }
  } catch (error) {
    console.warn('⚠️ Erro ao corrigir cache do Redux persist:', error);
    // Se houver erro, remover completamente
    localStorage.removeItem('persist:root');
  }
};

/**
 * Verifica se há cache corrompido de filtros de data
 */
export const hasCorruptedDateCache = () => {
  try {
    // Verificar Redux persist
    const persistRoot = localStorage.getItem('persist:root');
    if (persistRoot) {
      const parsed = JSON.parse(persistRoot);
      if (parsed.customers) {
        const customers = JSON.parse(parsed.customers);
        
        // Verificar objetos moment/dayjs não serializados
        if (
          (customers.dtStart && typeof customers.dtStart === 'object' && customers.dtStart._isAMomentObject) ||
          (customers.dtEnd && typeof customers.dtEnd === 'object' && customers.dtEnd._isAMomentObject)
        ) {
          return true;
        }
      }
    }
    
    // Verificar outros caches problemáticos
    for (const key of DATE_FILTER_CACHE_KEYS) {
      const item = localStorage.getItem(key);
      if (item) {
        try {
          const parsed = JSON.parse(item);
          if (parsed && typeof parsed === 'object' && parsed._isAMomentObject) {
            return true;
          }
        } catch (parseError) {
          // Se não conseguir fazer parse, considerar corrompido
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.warn('Erro ao verificar cache corrompido:', error);
    return true; // Em caso de erro, assumir que há corrupção
  }
};

/**
 * Força atualização de componentes de data
 */
export const forceUpdateDateComponents = () => {
  // Disparar evento customizado para componentes escutarem
  const event = new CustomEvent('dateFilterCacheCleared', {
    detail: { timestamp: Date.now() }
  });
  
  window.dispatchEvent(event);
  console.log('📡 Evento de limpeza de cache disparado');
};

/**
 * Inicializa verificação automática de cache na aplicação
 */
export const initializeDateCacheMonitoring = () => {
  console.log('🔍 Inicializando monitoramento de cache de filtros de data');
  
  // Verificar na inicialização
  if (hasCorruptedDateCache()) {
    console.log('⚠️ Cache corrompido detectado na inicialização');
    clearDateFilterCache();
    forceUpdateDateComponents();
  }
  
  // Verificar periodicamente (a cada 30 segundos)
  const intervalId = setInterval(() => {
    if (hasCorruptedDateCache()) {
      console.log('⚠️ Cache corrompido detectado durante execução');
      clearDateFilterCache();
      forceUpdateDateComponents();
    }
  }, 30000);
  
  // Limpar interval quando a página for fechada
  window.addEventListener('beforeunload', () => {
    clearInterval(intervalId);
  });
  
  return intervalId;
};

/**
 * Hook para detectar problemas de cache em tempo real
 */
export const useDateCacheMonitor = () => {
  const [cacheStatus, setCacheStatus] = useState('clean');
  
  useEffect(() => {
    const checkCache = () => {
      const isCorrupted = hasCorruptedDateCache();
      setCacheStatus(isCorrupted ? 'corrupted' : 'clean');
      
      if (isCorrupted) {
        clearDateFilterCache();
        forceUpdateDateComponents();
      }
    };
    
    // Verificar imediatamente
    checkCache();
    
    // Escutar evento de limpeza
    const handleCacheCleared = () => {
      setCacheStatus('clean');
    };
    
    window.addEventListener('dateFilterCacheCleared', handleCacheCleared);
    
    // Verificar periodicamente
    const interval = setInterval(checkCache, 10000); // A cada 10 segundos
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('dateFilterCacheCleared', handleCacheCleared);
    };
  }, []);
  
  return {
    cacheStatus,
    clearCache: () => {
      clearDateFilterCache();
      forceUpdateDateComponents();
    }
  };
};

/**
 * Adiciona headers anti-cache para requisições de componentes de data
 */
export const getAntiCacheHeaders = () => {
  return {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'X-Timestamp': Date.now().toString()
  };
};

/**
 * Gera uma chave única para forçar re-renderização
 */
export const generateCacheBustingKey = () => {
  return `date_filter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
