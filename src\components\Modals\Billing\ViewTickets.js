import React, { useMemo, useState } from "react";
import {
  Button,
  Col,
  DatePicker,
  Modal,
  Row,
  Select,
  Space,
  Table,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import {
  FileTextOutlined,
  CloseOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import moment from "moment";
import dayjs from "dayjs";
import {
  getTicketsByCustomerByDate,
  handleDateChange,
} from "../../../pages/Billing/controllers/complianceControllers";
import { formatTagViewOnTable } from "../../../controllers/reports/ticket-reports-controller";
import { SearchInput } from "../../SearchInput";
import { filterTableData } from "../../../utils/filterTableData";
import { Counter } from "../../Counter";
import { BarChart, Bar, XAxis, YAxis } from "recharts";
import { groupByParams } from "../../../utils/groupByParameter";
export const ViewTickets = (props) => {
  const { customerId, contract } = props;
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [search, setSearch] = useState("");
  const [date, setDate] = useState(moment());

  const columns = [
    {
      code: "view_ticket_number",
      title: "Número",
      dataIndex: "ticket_number",
      align: "center",
      render: (text) => {
        return (
          <Tooltip title="Visualizar ticket">
            <a
              href={`https://${
                process.env.REACT_APP_STAGE !== "prod" ? "hml." : ""
              }tickets.darede.com.br/otrs/index.pl?Action=AgentTicketZoom;TicketNumber=${text}`}
              target="_blank"
            >
              {text}
            </a>
          </Tooltip>
        );
      },
    },
    {
      code: "view_ticket_title",
      title: "Ticket",
      align: "center",
      dataIndex: "ticket_title",
    },
    {
      code: "view_ticket_customer",
      title: "Cliente",
      dataIndex: "customer_name",
    },
    {
      code: "view_ticket_contract",
      title: "Contrato",
      dataIndex: "contract_name",
    },
    {
      code: "view_ticket_owner",
      title: "Responsável",
      dataIndex: "owner",
    },
    {
      code: "time_spent",
      title: "Tempo gasto (min)",
      dataIndex: "total_time_spent",
    },
    {
      code: "view_ticket_status",
      title: "Status",
      align: "center",
      dataIndex: "ticket_state",
      render: (text) => {
        return formatTagViewOnTable(text);
      },
    },
  ];

  const filteredTickets = useMemo(() => {
    let filteredData = tickets || [];

    const searchFields = [
      "ticket_title",
      "customer_name",
      "contract_name",
      "ticket_state",
      "owner",
    ];

    if (search !== "") {
      filteredData = filterTableData({
        searchFields,
        search,
        data: filteredData,
      });
    }

    return filteredData;
  }, [tickets, search]);

  const ticketsTabs = [
    {
      label: "Listagem",
      key: "1",
      children: (
        <Table
          loading={loading}
          scroll={{ x: "100%" }}
          dataSource={filteredTickets}
          columns={columns}
        />
      ),
    },
    {
      label: "Visão gráfica",
      key: "2",
      children: (
        <ViewGraph
          data={filteredTickets}
          setSearch={setSearch}
          search={search}
          loading={loading}
        />
      ),
    },
  ];

  return (
    <Row justify="center">
      <Button
        type="text"
        onClick={async () => {
          setShowModal(true);
          await handleDateChange(
            dayjs(),
            setLoading,
            setSearch,
            setTickets,
            customerId,
            contract,
            setDate
          );
        }}
      >
        <FileTextOutlined />
      </Button>
      <Modal
        width="60vw"
        title="Tickets"
        open={showModal}
        onCancel={() => {
          setShowModal(false);
        }}
        open={showModal}
        footer={null}
      >
        <Row justify="space-between">
          <Col>
            <SearchInput
              placeholder="Buscar ticket"
              style={{ marginBottom: "20px" }}
              onChange={(e) => setSearch(e)}
              value={search}
            />
          </Col>
          <Col>
            <DatePicker
              value={date}
              clearIcon={false}
              picker="month"
              onChange={(e) =>
                handleDateChange(
                  e,
                  setLoading,
                  setSearch,
                  setTickets,
                  customerId,
                  contract,
                  setDate
                )
              }
              style={{ marginBottom: "20px" }}
            />
          </Col>
        </Row>
        <Counter tableData={filteredTickets} />
        <Tabs defaultActiveKey="1" items={ticketsTabs} />
      </Modal>
    </Row>
  );
};

const ViewGraph = (props) => {
  const { data, setSearch, search, loading } = props;
  const { Option } = Select;
  const { Text } = Typography;
  const [selectedOption, setSelectedOption] = useState("ticket_state");
  const [width, setWidth] = React.useState(window.innerWidth);
  let secondaryProportion = window.innerWidth;

  const formmatedData = useMemo(() => {
    let groupedByState = data || [];

    let formmatedData = [];
    if (selectedOption === "ticket_state") {
      groupedByState = groupByParams(data, "ticket_state");
      formmatedData = Object.keys(groupedByState).map((item) => {
        return {
          [selectedOption]: item,
          data: groupedByState[item],
        };
      });
    } else if (selectedOption === "owner") {
      groupedByState = groupByParams(data, "owner");
      formmatedData = Object.keys(groupedByState).map((item) => {
        return {
          [selectedOption]: item,
          data: groupedByState[item],
        };
      });
    }

    return formmatedData;
  }, [data, selectedOption]);

  const updateWidth = () => {
    setWidth(window.innerWidth);
  };
  React.useEffect(() => {
    window.addEventListener("resize", updateWidth);

    return () => window.removeEventListener("resize", updateWidth);
  });

  const graphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 750;
      return width / proportion;
    } else if ((width) => 1200 && width < 1800) {
      const proportion = 1850 / 1000;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 500;
      return width / proportion;
    }
  }, [width]);

  const secondaryGraphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if ((width) => 1200 && width < 1800) {
      const proportion = 1850 / 1250;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 1600;
      return width / proportion;
    }
  }, [width]);

  return (
    <>
      {loading ? (
        <Row justify="center" style={{ fontSize: "48px" }}>
          <LoadingOutlined />
        </Row>
      ) : (
        <div>
          {formmatedData.length === 0 ? (
            <Row justify="center">
              <Text type="secondary">
                Nenhum ticket encontrado com os parâmetros de busca
              </Text>
            </Row>
          ) : (
            <>
              <Space size="middle">
                <Text>Agrupar por: </Text>
                <Select
                  defaultValue="ticket_state"
                  style={{ width: 120 }}
                  onChange={(value) => {
                    setSelectedOption(value);
                  }}
                >
                  <Option value="ticket_state">Status</Option>
                  <Option value="owner">Responsável</Option>
                </Select>
                {search && (
                  <Tag
                    color="success"
                    closable={true}
                    closeIcon={<CloseOutlined />}
                    onClose={() => {
                      setSearch("");
                    }}
                  >
                    {search}
                  </Tag>
                )}
              </Space>
              <BarChart
                width={
                  secondaryProportion
                    ? width < 1600 && width > 1200
                      ? secondaryGraphWidth - 180
                      : width < 1200
                      ? secondaryGraphWidth - 280
                      : secondaryGraphWidth - 160
                    : width < 1600
                    ? graphWidth - 300
                    : graphWidth - 150
                }
                height={400}
                data={formmatedData}
                style={{ margin: "2em 0" }}
              >
                <YAxis allowDecimals={false} />
                <XAxis
                  dataKey={selectedOption}
                  angle={selectedOption === "owner" ? -25 : 0}
                  textAnchor="end"
                  height={100}
                />
                <Bar
                  cursor={"pointer"}
                  onClick={(e) => {
                    setSearch(e.payload[selectedOption]);
                  }}
                  dataKey={(item) => item.data["qtd"]}
                  fill="#333"
                  label={{
                    position: "center",
                    fontSize: "1.2rem",
                  }}
                />
              </BarChart>
            </>
          )}
        </div>
      )}
    </>
  );
};
