import { createSlice } from "@reduxjs/toolkit";
import dayjs from "dayjs";

import { COLLUMNS_CONSUMPTION_HOURS } from "./consumption-hours-reducer";

const REDUCER_NAME = "globalPersist";

const INITIAL_STATE = {
  consumption: {
    collumnsToShow: COLLUMNS_CONSUMPTION_HOURS,
  },
  technicalProposal: {
    loadingPersist: false,
  },
};

const globalPersistSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setGlobalPersistReduce(state, action) {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }
      state[action.payload.field] = value;
    },
  },
});

export const { setGlobalPersistReduce } = globalPersistSlice.actions;

export default globalPersistSlice.reducer;
