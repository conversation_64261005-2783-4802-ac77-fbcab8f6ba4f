export const customers = [
  {
    id: "customer1",
    accounts: [
      {
        account_id: "***************",
      },
    ],
    active: 1,
    cnpj: "11.111.111/11111-11",
    extent: {
      id: "05",
      descricao: "<PERSON><PERSON><PERSON>",
    },
    identifications: {
      crm_id: 6723874528374592367435780934787623,
      itsm_id: "11111",
    },
    names: {
      fantasy_name: "Mo<PERSON> Mai<PERSON>",
      name: "BANCO MODAL S.A.",
    },
  },
  {
    id: "customer2",
    accounts: [
      {
        account_id: "736475623497582347563476",
      },
    ],
    active: 1,
    cnpj: "22.222.222/22222-22",
    extent: {
      id: "05",
      descricao: "telecom",
    },
    identifications: {
      crm_id: 22222,
      itsm_id: "********",
    },
    names: {
      fantasy_name: "Telecom Ficticia",
      name: "EMPRESA TELECOM FICTICIA S.A.",
    },
  },
  {
    id: "customer3",
    accounts: [
      {
        account_id: "***************",
      },
    ],
    active: 1,
    cnpj: "33.333.333/33333-33",
    extent: {
      id: "05",
      descricao: "Saneamento",
    },
    identifications: {
      crm_id: *********,
      itsm_id: "333",
    },
    names: {
      fantasy_name: "Saneamento Ficticia",
      name: "EMPRESA SANEAMENTO FICTICIA S.A.",
    },
  },
  {
    id: "customer4",
    accounts: [
      {
        account_id: "176837569743923476",
      },
    ],
    active: 1,
    cnpj: "44.444.444/44444-44",
    extent: {
      id: "05",
      descricao: "Startup",
    },
    identifications: {
      crm_id: *********,
      itsm_id: "*********444444",
    },
    names: {
      fantasy_name: "Modal Mais",
      name: "STARTUP FICTICIA S.A.",
    },
  },
  {
    id: "customer5",
    accounts: [
      {
        account_id: "**************",
      },
    ],
    active: 1,
    cnpj: "55.555.555/55555-55",
    extent: {
      id: "05",
      descricao: "Agro",
    },
    identifications: {
      crm_id: *********,
      itsm_id: "*********555555",
    },
    names: {
      fantasy_name: "Agro Ficticia",
      name: "AGRO FICTICIA S.A.",
    },
  },
];

export const contracts = [
  // Customer1: 1 contrato ativo - deve aparecer no filtro ativosA
  {
    active: 1,
    customer: {
      id: "customer1",
      itsm_id: "11111",
    },
    name: "Contrato ativo 1",
  },
  // Customer2: 1 contrato ativo + 1 inativo - apenas o ativo deve aparecer no filtro ativosA
  {
    active: 1,
    customer: {
      id: "customer2",
      itsm_id: "********",
    },
    name: "Contrato ativo 2",
  },
  {
    active: 0,
    customer: {
      id: "customer2",
      itsm_id: "********",
    },
    name: "Contrato inativo 2",
  },
  // Customer3: apenas contratos inativos - NÃO deve aparecer no filtro ativosA
  {
    active: 0,
    customer: {
      id: "customer3",
      itsm_id: "333",
    },
    name: "Contrato inativo 3a",
  },
  {
    active: 0,
    customer: {
      id: "customer3",
      itsm_id: "333",
    },
    name: "Contrato inativo 3b",
  },
  // Customer4: 1 contrato ativo - deve aparecer no filtro ativosA
  {
    active: 1,
    customer: {
      id: "customer4",
      itsm_id: "*********444444",
    },
    name: "Contrato ativo 4",
  },
  // Customer5: apenas contrato inativo - NÃO deve aparecer no filtro ativosA
  {
    active: 0,
    customer: {
      id: "customer5",
      itsm_id: "*********555555",
    },
    name: "Contrato inativo 5",
  },
];

export const fields = [
  {
    label: "Clientes",
    subItems: [
      "Data de Criação",
      "CRM do cliente",
      "ITSM do cliente",
      "Nome do cliente",
      "Nome fantasia",
      "CNPJ",
    ],
  },
  {
    label: "Contatos",
    subItems: [
      "CRM do contato",
      "ITSM do contato",
      "Primeiro nome",
      "Último nome",
      "Telefone",
      "Email",
    ],
  },
];

export const data = [
  {
    id: "customer1",
    created_at: new Date(),
    identifications: {
      crm_id: 6723874528374592367435780934787623,
      itsm_id: "11111",
    },
    names: {
      fantasy_name: "Modal Mais",
      name: "BANCO MODAL S.A.",
    },
    cnpj: "11.111.111/11111-11",
    contacts: [
      {
        names: { first_name: "first_name", last_name: "last_name" },
        identifications: {
          crm_id: 6723874528374592367435780934787623,
          itsm_id: "4545",
        },
        phone: "(11)2222-2222",
        email: "<EMAIL>",
      },
    ],
  },
];
