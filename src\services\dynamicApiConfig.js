/**
 * Serviço de Configuração Dinâmica da API
 * Busca a configuração real da API e configura URLs automaticamente
 */

import axios from 'axios';

class DynamicApiConfigService {
  constructor() {
    this.config = null;
    this.isConfigured = false;
    this.configPromise = null;
  }

  /**
   * Busca a configuração da API dinamicamente
   */
  async fetchApiConfig() {
    if (this.configPromise) {
      return this.configPromise;
    }

    this.configPromise = this._doFetchConfig();
    return this.configPromise;
  }

  async _doFetchConfig() {
    // Usar configuração das variáveis de ambiente
    // Respeita o .env para desenvolvimento local ou produção
    this.config = {
      axios: {
        baseURL: process.env.REACT_APP_API_PERMISSION,
        // REMOVIDO: withCredentials para evitar CORS
      },
      endpoints: {
        auth: '/auth',
        cognito: '/cognito/read',
        mfaSecrets: '/mfa/secrets'
      }
    };

    await this.configureAxios();

    return this.config;
  }

  /**
   * Configura o axios com as URLs corretas
   */
  async configureAxios() {
    if (!this.config) {
      throw new Error('Configuração não disponível');
    }

    const { axios: axiosConfig } = this.config;
    
    console.log('⚙️ Configurando axios com:', axiosConfig);
    
    // Não modificar axios global para evitar conflitos
    // Apenas armazenar a configuração para uso posterior
    this.axiosConfig = {
      baseURL: axiosConfig.baseURL,
      withCredentials: axiosConfig.withCredentials || true,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    this.isConfigured = true;
    console.log('✅ Axios configurado com sucesso');
  }

  /**
   * Obtém a URL completa para um endpoint
   */
  getEndpointUrl(endpoint) {
    if (!this.config) {
      console.warn('⚠️ Configuração não disponível, usando URL padrão das variáveis de ambiente');
      const defaultBaseURL = process.env.REACT_APP_API_PERMISSION;
      return `${defaultBaseURL}${endpoint}`;
    }

    const baseURL = this.config.axios.baseURL;
    const endpointPath = this.config.endpoints?.[endpoint] || endpoint;

    return `${baseURL}${endpointPath}`;
  }

  /**
   * Verifica se a configuração está pronta
   */
  isReady() {
    return this.isConfigured && this.config !== null;
  }

  /**
   * Obtém a configuração atual
   */
  getConfig() {
    return this.config;
  }

  /**
   * Força uma nova busca da configuração
   */
  async refreshConfig() {
    this.config = null;
    this.isConfigured = false;
    this.configPromise = null;
    
    return this.fetchApiConfig();
  }

  /**
   * Cria uma instância do axios configurada
   */
  createConfiguredAxios() {
    if (!this.axiosConfig) {
      throw new Error('Configuração não disponível. Chame fetchApiConfig() primeiro.');
    }

    const instance = axios.create(this.axiosConfig);

    // Interceptors para a instância (com validação)
    instance.interceptors.request.use(
      (config) => {
        const url = config.url || '';
        const method = config.method?.toUpperCase() || 'GET';
        console.log(`📡 Instance Request: ${method} ${url}`);
        return config;
      },
      (error) => {
        console.error('❌ Instance Request Error:', error);
        return Promise.reject(error);
      }
    );

    instance.interceptors.response.use(
      (response) => {
        const url = response.config?.url || '';
        console.log(`✅ Instance Response: ${response.status} ${url}`);
        return response;
      },
      (error) => {
        const url = error.config?.url || '';
        const status = error.response?.status || 'unknown';
        console.error(`❌ Instance Error: ${status} ${url}`, error.message);
        return Promise.reject(error);
      }
    );

    return instance;
  }
}

// Instância singleton
export const dynamicApiConfig = new DynamicApiConfigService();

export async function initializeApiConfig() {
  try {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      return true;
    }

    await dynamicApiConfig.fetchApiConfig();
    return true;
  } catch (error) {
    return false;
  }
}

// Função utilitária para fazer requisições com configuração dinâmica
export async function makeConfiguredRequest(endpoint, options = {}) {
  if (!dynamicApiConfig.isReady()) {
    await dynamicApiConfig.fetchApiConfig();
  }

  const axiosInstance = dynamicApiConfig.createConfiguredAxios();
  
  return axiosInstance({
    url: endpoint,
    ...options
  });
}

export default dynamicApiConfig;
