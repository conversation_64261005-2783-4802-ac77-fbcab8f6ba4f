/**
 * Configuração global de autenticação
 * Gerencia o estado entre cookies HttpOnly e localStorage
 */

import { logger } from './logger';

class AuthConfigManager {
  constructor() {
    this.config = {
      httpOnlySupported: false,
      initialized: false,
      fallbackMode: false
    };
    
    this.initializeGlobalConfig();
  }

  /**
   * Inicializar configuração global no window
   */
  initializeGlobalConfig() {
    if (typeof window !== 'undefined') {
      window.__AUTH_CONFIG__ = this.config;
    }
  }

  /**
   * Detectar suporte a cookies HttpOnly
   */
  async detectHttpOnlySupport() {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_PERMISSION}auth/config`, {
        method: 'GET',
        credentials: 'include', // Incluir cookies
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.setHttpOnlySupport(data.auth?.httpOnlySupported || false);
        logger.info('Configuração de autenticação detectada:', {
          httpOnlySupported: this.config.httpOnlySupported
        });
        return this.config.httpOnlySupported;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      logger.warn('Falha na detecção de suporte a cookies HttpOnly, usando fallback:', error.message);
      this.setHttpOnlySupport(false);
      this.config.fallbackMode = true;
      return false;
    }
  }

  /**
   * Definir suporte a cookies HttpOnly
   */
  setHttpOnlySupport(supported) {
    this.config.httpOnlySupported = supported;
    this.config.initialized = true;
    
    // Atualizar configuração global
    if (typeof window !== 'undefined') {
      window.__AUTH_CONFIG__ = { ...this.config };
    }

    logger.debug('Configuração de autenticação atualizada:', this.config);
  }

  /**
   * Verificar se cookies HttpOnly são suportados
   */
  isHttpOnlySupported() {
    return this.config.httpOnlySupported;
  }

  /**
   * Verificar se está em modo fallback
   */
  isFallbackMode() {
    return this.config.fallbackMode;
  }

  /**
   * Verificar se foi inicializado
   */
  isInitialized() {
    return this.config.initialized;
  }

  /**
   * Obter configuração atual
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Aguardar inicialização
   */
  async waitForInitialization(timeout = 5000) {
    const startTime = Date.now();
    
    while (!this.config.initialized && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (!this.config.initialized) {
      logger.warn('Timeout na inicialização da configuração de autenticação');
      this.setHttpOnlySupport(false); 
    }
    
    return this.config.initialized;
  }

  /**
   * Reset da configuração (para testes)
   */
  reset() {
    this.config = {
      httpOnlySupported: false,
      initialized: false,
      fallbackMode: false
    };
    this.initializeGlobalConfig();
  }
}

export const authConfigManager = new AuthConfigManager();

export default authConfigManager;

/**
 * Função utilitária para verificar se cookies HttpOnly são suportados
 */
export const isHttpOnlySupported = () => {
  return authConfigManager.isHttpOnlySupported();
};

/**
 * Função utilitária para aguardar inicialização
 */
export const waitForAuthConfig = (timeout) => {
  return authConfigManager.waitForInitialization(timeout);
};
