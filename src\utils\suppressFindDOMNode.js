/**
 * Supressão específica para warnings do findDOMNode
 * Este arquivo deve ser importado o mais cedo possível para interceptar todos os warnings
 */

// Armazenar referências originais
const originalError = console.error;
const originalWarn = console.warn;

// Função para verificar se a mensagem deve ser suprimida
const shouldSuppressFindDOMNodeMessage = (message) => {
  const lowerMessage = message.toLowerCase();
  return lowerMessage.includes('finddomnode is deprecated') ||
         lowerMessage.includes('finddomnode was passed an instance') ||
         lowerMessage.includes('instead, add a ref directly to the element') ||
         lowerMessage.includes('learn more about using refs safely here') ||
         lowerMessage.includes('reactjs.org/link/strict-mode-find-node') ||
         lowerMessage.includes('singleobserver') ||
         lowerMessage.includes('resizeobserver') ||
         lowerMessage.includes('will be removed in the next major release') ||
         // Variações específicas do warning
         message.includes('findDOMNode is deprecated') ||
         message.includes('findDOMNode was passed an instance') ||
         message.includes('Instead, add a ref directly to the element') ||
         message.includes('Learn more about using refs safely here') ||
         message.includes('https://reactjs.org/link/strict-mode-find-node') ||
         message.includes('SingleObserver') ||
         message.includes('ResizeObserver') ||
         message.includes('will be removed in the next major release');
};

// Sobrescrever console.error
console.error = (...args) => {
  const message = args.join(' ');
  
  if (shouldSuppressFindDOMNodeMessage(message)) {
    return; // Suprimir a mensagem
  }
  
  // Chamar o console.error original para outras mensagens
  originalError.apply(console, args);
};

// Sobrescrever console.warn
console.warn = (...args) => {
  const message = args.join(' ');
  
  if (shouldSuppressFindDOMNodeMessage(message)) {
    return; // Suprimir a mensagem
  }
  
  // Chamar o console.warn original para outras mensagens
  originalWarn.apply(console, args);
};

// Exportar função para restaurar console original se necessário
export const restoreOriginalConsole = () => {
  console.error = originalError;
  console.warn = originalWarn;
};

// Interceptar warnings do React DevTools também
if (typeof window !== 'undefined') {
  // Interceptar possíveis warnings do React DevTools
  const originalSetTimeout = window.setTimeout;
  window.setTimeout = function(callback, delay, ...args) {
    if (typeof callback === 'function') {
      const wrappedCallback = function() {
        try {
          return callback.apply(this, arguments);
        } catch (error) {
          if (error.message && shouldSuppressFindDOMNodeMessage(error.message)) {
            return; // Suprimir erro relacionado ao findDOMNode
          }
          throw error;
        }
      };
      return originalSetTimeout.call(this, wrappedCallback, delay, ...args);
    }
    return originalSetTimeout.call(this, callback, delay, ...args);
  };
}


