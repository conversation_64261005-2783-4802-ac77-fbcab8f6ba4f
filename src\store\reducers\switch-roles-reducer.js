import { createSlice } from "@reduxjs/toolkit";
import dayjs from "dayjs";

const REDUCER_NAME = "switchRole";

const INITIAL_STATE = {
  permissionSets: [],
  switchRoleData: [],
};

const switchRoleSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setSwitchRoleStateReduce(state, action) {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }

      state[action.payload.field] = value;
    },
  },
});

export const { setSwitchRoleStateReduce } = switchRoleSlice.actions;

export default switchRoleSlice.reducer;
