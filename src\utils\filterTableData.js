export const filterTableData = ({ searchFields, search, data }) => {
  try {
    if (!data || !searchFields) return [];
    if (search === "") {
      return data;
    } else {
      return data.filter((item) => {
        return searchFields.some((key) => {
          if (typeof item[key] === "object") {
            return JSON.stringify(item[key])
              .toLowerCase()
              .includes(search.toLowerCase());
          } else if (item[key]) {
            return item[key]
              ? item[key]
                  .toString()
                  .toLowerCase()
                  .includes(search.toLowerCase())
              : "";
          }
        });
      });
    }
  } catch (error) {
    console.error('Erro em filterTableData:', error);
    return []; 
  }
};
