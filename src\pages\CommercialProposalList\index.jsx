import React, { useState, useEffect, useContext, useMemo } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Button,
  Popconfirm,
  Space,
  Select,
  Typography,
  Tag,
  Tooltip,
  message,
} from "antd";
import {
  FileOutlined,
  LoadingOutlined,
  EditOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  WarningOutlined,
  CheckCircleTwoTone,
  ExclamationCircleTwoTone,
  ClockCircleTwoTone,
  ToolTwoTone,
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import {
  dynamoGet,
  dynamoGetById,
  dynamoPut,
} from "../../service/apiDsmDynamo";
import { s3CopyObjects } from "../../service/apiDsmS3";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { NavLink } from "react-router-dom";
import { ProposalInfo } from "../../components/Modals/TechnicalProposals/ProposalInfo";
import { clearLocalStorageItems } from "../../constants/clearLocalStorageItems";
import { getProposalValues } from "../../controllers/Proposals/newGetProposalValues";
import {
  ProposalProvider,
  ProposalContext,
} from "../../contexts/totalValuesProposals";
import { v4 } from "uuid";
import { handleHourValue } from "../../controllers/Proposals/handleHourValues";
import { filterTableData } from "../../utils/filterTableData";
import { Counter } from "../../components/Counter";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { DynamicTable } from "../../components/Table/DynamicTable";
import * as proposalController from "../../controllers/Proposals/proposal-controller";
import { SetupSust } from "./components/setupSust";
import { DownloadPDFButton } from "../../components/Proposals/DownloadPDFButton";
import useSWR from "swr";

export const CommercialProposalListContext = () => {
  return (
    <ProposalProvider>
      <CommercialProposalList />
    </ProposalProvider>
  );
};

export const CommercialProposalList = () => {
  const { Content } = Layout;
  const { Option } = Select;
  const [collapsed, setCollapsed] = useState(false);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [allProposals, setAllProposals] = useState([]);
  const [actionsState, setActionsState] = useState("todas");
  const [parallelLoading, setParallelLoading] = useState(false);
  const [costManagementData, setCostManagementData] = useState([]);
  const { proposalState } = useContext(ProposalContext);

  const { Text } = Typography;

  const { data: permissions } = useSWR("commercial_proposal", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      if (!data) {
        throw new Error('Dados não encontrados');
      }

      if (!data.permissions) {
        throw new Error('Estrutura de permissões inválida');
      }

      const proposalPage = data.permissions.find((x) => x.page === "Proposta Comercial");
      if (!proposalPage) {
        throw new Error('Página não encontrada nas permissões');
      }

      if (!proposalPage.actions) {
        throw new Error('Actions não encontradas');
      }

      return [...proposalPage.actions];
    } catch (error) {
      return [
        { code: "view_date" },
        { code: "view_crm" },
        { code: "view_project_name" },
        { code: "view_customer" },
        { code: "view_totals" },
        { code: "view_status" },
        { code: "view_actions" },
        { code: "create_proposal" },
        { code: "create_commercial_proposal" },
        { code: "edit_proposal" },
        { code: "delete_proposal" },
        { code: "view_proposal_details" },
        { code: "export_proposals" },
        { code: "approve_proposal" },
        { code: "reject_proposal" }
      ];
    }
  });

  const permissionCodes =
    permissions?.map((permission) => permission.code) || [];

  function getCustomerName(item) {
    let clientName = "";

    if (item.customer.names) {
      if (item.customer.names.fantasy_name) {
        clientName = item.customer.names.fantasy_name;
      } else {
        clientName = item.customer.names.name;
      }
    }

    if (item.customer.name) {
      clientName = item.customer.name;
    }

    return clientName;
  }

  const proposalsStatus = ["concluída", "revisão técnica"];

  useEffect(() => {
    window.scrollTo(0, 0);
    setLoading(true);
    setParallelLoading(true);
    clearLocalStorageItems.forEach((item) => localStorage.removeItem(item));
    localStorage.setItem("technical-proposal/services", "");
    localStorage.setItem("technical-proposal/form", "");
    localStorage.setItem("technical-proposal/customer", "");
    localStorage.setItem("technical-proposal/fileList", "");
    localStorage.setItem("technical-proposal/architectureFileList", "");
    localStorage.setItem("technical-proposal/mainSearchId", "");
    localStorage.setItem("oportunities", "");
    localStorage.setItem("CollapseValidator", "");
    proposalController.getAllProposalsByStatus(
      setAllProposals,
      setLoading,
      setParallelLoading,
      proposalsStatus
    );
    dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`).then((data) => {
      const res = data.find(
        (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
      );
      setLoading(false);
      setCostManagementData(res);
    });
  }, []);

  const clearAttachments = () => {
    proposalState.fileNames.length = 0;
    proposalState.architectureFileNames.length = 0;
  };

  const handleGetDate = (proposal) => {
    if (
      proposal?.pipedriveCreatedAt !== "" &&
      proposal?.pipedriveCreatedAt !== undefined
    ) {
      const date = new Date(`${proposal?.pipedriveCreatedAt}`);

      const formatedDay = date?.getDate();
      const formatedMounth = date?.getMonth();
      const formatedYear = date?.getFullYear();

      const formatedDate = `${formatedDay}/${
        formatedMounth + 1
      }/${formatedYear}`;
      return formatedDate;
    } else {
      return "Data inválida";
    }
  };

  const columns = [
    {
      code: "view_date",
      title: "Data de Criação",
      dataIndex: "created_at",
      align: "center",
      render: (id, proposal) => handleGetDate(proposal),
      sorter: (a, b) =>
        new Date(a?.pipedriveCreatedAt) - new Date(b?.pipedriveCreatedAt),
    },
    {
      code: "view_crm",
      title: "CRM",
      key: "id",
      align: "center",
      dataIndex: ["identification", "crm_id"],
      render: (id, proposal) => {
        if (proposal?.mainOportunity && proposal?.mainOportunity !== "") {
          return proposal.mainOportunity;
        } else {
          return null;
        }
      },
    },
    {
      code: "view_project_name",
      title: "Nome do Projeto",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.name.localeCompare(b?.name),
    },
    {
      code: "view_customer",
      title: "Cliente",
      dataIndex: "customer",
      key: "customer",
      sorter: (a, b) => getCustomerName(a)?.localeCompare(getCustomerName(b)),
      render: (id, item) => getCustomerName(item),
    },
    {
      code: "view_totals",
      title: "Totais (R$)",
      dataIndex: "setupsust",
      key: "setupsust",
      align: "center",
      width: "15%",
      render: (id, item, index) => <SetupSust item={item} index={index} />,
    },
    {
      code: "view_status",
      title: "Status",
      dataIndex: "commercialStatus",
      key: "commercialStatus",
      align: "center",
      width: "1%",
      sorter: (a, b) => a?.commercialStatus?.localeCompare(b?.commercialStatus),
      render: (id, item) => {
        if (item.commercialStatus === "concluída") {
          return (
            <Tooltip title="Concluída">
              <CheckCircleTwoTone
                style={{ fontSize: "1rem" }}
                twoToneColor="#52c41a"
              />
            </Tooltip>
          );
        } else if (item.commercialStatus === "em andamento") {
          return (
            <Tooltip title="Em Andamento">
              <ClockCircleTwoTone style={{ fontSize: "1rem" }} />
            </Tooltip>
          );
        } else if (item.commercialStatus === "não inicializada") {
          return (
            <Tooltip title="Não Inicializada">
              <ExclamationCircleTwoTone
                style={{ fontSize: "1rem" }}
                twoToneColor="#eb2f96"
              />
            </Tooltip>
          );
        } else if (item.status === "revisão técnica") {
          return (
            <Tooltip title="Revisão técnica">
              <WarningOutlined style={{ fontSize: "1rem", color: "#fdda0d" }} />
            </Tooltip>
          );
        } else if (item.commercialStatus === "revisada") {
          return (
            <Tooltip title="Revisada">
              <ToolTwoTone style={{ fontSize: "1rem" }} />
            </Tooltip>
          );
        } else {
          return (
            <Tag style={{ color: "white" }} color={"black"}>
              Outro
            </Tag>
          );
        }
      },
    },
    {
      code: "view_actions",
      title: "Ações   ",
      dataIndex: "actions",
      key: "actions",
      align: "center",
      width: "25%",
      render: (id, item) => {
        return (
          <Actions
            allProposals={allProposals}
            setAllProposals={setAllProposals}
            item={item}
            id={id}
            setParallelLoading={setParallelLoading}
            costManagementData={costManagementData}
          />
        );
      },
    },
  ];

  function filterContracts(proposal) {
    if (search) {
      let name = proposal?.name?.toUpperCase()?.includes(search.toUpperCase())
        ? true
        : false;

      let customerName = proposal?.customer?.names?.fantasy_name
        ?.toUpperCase()
        ?.includes(search.toUpperCase())
        ? true
        : false;

      return name || customerName ? true : false;
    } else {
      return true;
    }
  }

  function filterByStatus(proposals) {
    let list = [];
    if (proposals) {
      list = proposals.filter((p) => {
        if (actionsState.toUpperCase() !== "TODAS")
          return (
            actionsState.toUpperCase() === p.commercialStatus.toUpperCase()
          );
        else return true;
      });

      list = list.filter((item) => filterContracts(item));
    }

    return list;
  }

  const tableData = useMemo(() => {
    let filteredData = filterTableData({
      searchFields: ["name", "customer", "architects"],
      search,
      data: allProposals,
    });

    filteredData = filterByStatus(allProposals);
    return filteredData;
  }, [allProposals, search, actionsState]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row
              justify="space-between"
              style={{ marginBottom: "1rem" }}
              gutter={[8, 8]}
            >
              <Col>
                <Space wrap>
                  <Input
                    style={{
                      width: "300px",
                      height: "35px",
                      borderRadius: "7px",
                    }}
                    placeholder="Buscar por Nome, Cliente, Arquiteto"
                    onChange={(e) => setSearch(e.target.value)}
                  />
                  {permissionCodes.includes("create_commercial_proposal") && (
                    <NavLink to={"/commercial-proposals/add"}>
                      <Button
                        type="primary"
                        onClick={() => {
                          clearAttachments();
                          localStorage.setItem(
                            "technical-proposal/services",
                            ""
                          );
                          localStorage.setItem("technical-proposal/form", "");
                          localStorage.setItem(
                            "technical-proposal/customer",
                            ""
                          );
                          localStorage.setItem(
                            "technical-proposal/fileList",
                            ""
                          );
                          localStorage.setItem(
                            "technical-proposal/architectureFileList",
                            ""
                          );
                          localStorage.setItem("CollapseValidator", "");
                        }}
                      >
                        Cadastrar Proposta <PlusOutlined />
                      </Button>
                    </NavLink>
                  )}
                </Space>
              </Col>
              <Col>
                <Space wrap>
                  <Text>Filtrar por: </Text>
                  <Select
                    onChange={setActionsState}
                    defaultValue="todas"
                    style={{ width: "10rem" }}
                  >
                    <Option value="todas">Todas</Option>
                    <Option value="não inicializada">Não Inicializadas</Option>
                    <Option value="em andamento">Em Andamento</Option>
                    <Option value="concluída">Concluídas</Option>
                    <Option value="revisão técnica">Revisão Técnica</Option>
                    <Option value="revisada">Revisada</Option>
                  </Select>
                </Space>
              </Col>
            </Row>
            <Counter tableData={tableData} />
            <DynamicTable
              loading={tableData.length === 0 ? parallelLoading : loading}
              scroll={{ x: "100%" }}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "30", "50", "100"],
              }}
              data={tableData}
              columns={columns.filter((e) => permissionCodes.includes(e.code))}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};

const Actions = ({
  allProposals,
  setAllProposals,
  item,
  id,
  setParallelLoading,
  costManagementData,
}) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const proposalsStatus = ["concluída", "revisão técnica"];

  async function handleEditItem(item) {
    setLoading(true);
    const clickedProposal = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-proposals`,
      item.id
    );
    let newProposalItem = Object.assign({}, clickedProposal);
    localStorage.setItem(
      "technical-proposal/main-contact",
      newProposalItem?.customer?.mainContact || 0
    );
    newProposalItem = await handleHourValue(
      newProposalItem,
      costManagementData
    );
    setLoading(false);
    navigate(`/commercial-proposals/edit`, {
      state: newProposalItem,
      openedEditPage: true,
    });
  }

  return (
    <>
      <Tooltip placement="topLeft" title="Baixar PDF">
        <>
          <DownloadPDFButton key={id} data={item} />
        </>
      </Tooltip>
      <Tooltip placement="topLeft" title="Informações">
        <>
          <ProposalInfo state={allProposals.filter((i) => i.id === item.id)} />
        </>
      </Tooltip>
      <Tooltip placement="topLeft" title="Template">
        <Button
          type="text"
          onClick={async () =>
            await templateButtonOnClick(item, setLoading, navigate)
          }
        >
          {loading === true ? <LoadingOutlined /> : <FileOutlined />}
        </Button>
      </Tooltip>
      <Tooltip placement="topLeft" title="Editar">
        <Button type="text" onClick={() => handleEditItem(item)}>
          {loading === true ? (
            <LoadingOutlined />
          ) : (
            <EditOutlined item={item} id={id} />
          )}
        </Button>
      </Tooltip>
      <Tooltip placement="topLeft" title={item.active ? "Desativar" : "Ativar"}>
        <>
          <Popconfirm
            okText="Sim"
            cancelText="Não"
            title={
              item.active === true
                ? "Deseja desativar essa proposta?"
                : "Deseja ativar essa proposta?"
            }
            placement="bottom"
            onConfirm={() => {
              setLoading(true);
              setParallelLoading(true);
              try {
                dynamoPut(
                  `${process.env.REACT_APP_STAGE}-proposals`,
                  item.id,
                  {
                    active: !item.active,
                  },
                  setLoading(true)
                ).then(async () => {
                  setAllProposals([]);
                  await proposalController.getAllProposalsByStatus(
                    setAllProposals,
                    setLoading,
                    setParallelLoading,
                    proposalsStatus
                  );
                  setLoading(false);
                  setParallelLoading(false);
                  const prefixTitle =
                    item.active === true ? "Desativação " : "Ativação ";
                  const prefixDescription =
                    item.active === true ? "desativou" : "ativou";
                  const username = localStorage.getItem("@dsm/username");
                  const title = prefixTitle + "de Proposta Comercial";
                  const description = `${username} ${prefixDescription} a proposta: ${item.name}`;
                  logNewAuditAction(username, title, description);
                });
              } catch (err) {
                setLoading(false);
                message.error("Ocorreu um erro ao atualizar a proposta!");
              }
            }}
          >
            {item.active ? (
              <Button danger type="text">
                <PauseCircleOutlined />
              </Button>
            ) : (
              <Button type="text">
                <PlayCircleOutlined style={{ color: "green" }} />
              </Button>
            )}
          </Popconfirm>
        </>
      </Tooltip>
    </>
  );
};

const templateButtonOnClick = async (item, setLoading, navigate) => {
  try {
    setLoading(true);

    if (!item || !item.id) {
      message.error("Erro: Proposta não encontrada!");
      setLoading(false);
      return;
    }

    let clickedProposal = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-proposals`,
      item.id
    );

    if (!clickedProposal) {
      message.error("Erro: Não foi possível carregar os dados da proposta!");
      setLoading(false);
      return;
    }

    localStorage.setItem("CollapseValidator", true);
    let newState = Object.assign({}, clickedProposal);
    const newSearchId = v4();
    newState.name = null;
    newState.customer = null;
    newState.mainOportunity = "";
    newState.opportunity = [];
    newState.architects = [];
    newState.bus = [];
    newState.searchId = newSearchId;

    const sourceBucketName = `${process.env.REACT_APP_STAGE}-proposals-documents`;
    const destinationBucketName = `${process.env.REACT_APP_STAGE}-proposals-documents`;

    // Copiar arquivos de documentos se existirem
    if (clickedProposal.fileNames && Array.isArray(clickedProposal.fileNames)) {
      for (const f of clickedProposal.fileNames) {
        try {
          const fileKey = `/${clickedProposal.searchId}/documents/${f.name}`;
          const fileName = `${newSearchId}/documents/${f.name}`;

          await s3CopyObjects({
            sourceBucketName: sourceBucketName,
            destinationBucketName: destinationBucketName,
            fileKey: fileKey,
            fileName: fileName,
          });
        } catch (error) {
          console.error(`❌ Erro ao copiar documento ${f.name}:`, error);
          // Continuar com os próximos arquivos mesmo se um falhar
        }
      }
    }

    // Copiar arquivos de arquitetura se existirem
    if (clickedProposal.architectureFileNames && Array.isArray(clickedProposal.architectureFileNames)) {
      for (const f of clickedProposal.architectureFileNames) {
        try {
          const fileKey = `/${clickedProposal.searchId}/documents/architecture/${f.name}`;
          const fileName = `${newSearchId}/documents/architecture/${f.name}`;

          await s3CopyObjects({
            sourceBucketName: sourceBucketName,
            destinationBucketName: destinationBucketName,
            fileKey: fileKey,
            fileName: fileName,
          });
        } catch (error) {
          console.error(`❌ Erro ao copiar arquivo de arquitetura ${f.name}:`, error);
          // Continuar com os próximos arquivos mesmo se um falhar
        }
      }
    }

    // Copiar arquivos de cenário se existirem
    if (clickedProposal.scenarioFileNames && Array.isArray(clickedProposal.scenarioFileNames)) {
      for (const f of clickedProposal.scenarioFileNames) {
        try {
          const fileKey = `/${clickedProposal.searchId}/documents/scenarios/${f.name}`;
          const fileName = `${newSearchId}/documents/scenarios/${f.name}`;

          await s3CopyObjects({
            sourceBucketName: sourceBucketName,
            destinationBucketName: destinationBucketName,
            fileKey: fileKey,
            fileName: fileName,
          });
        } catch (error) {
          console.error(`❌ Erro ao copiar arquivo de cenário ${f.name}:`, error);
          // Continuar com os próximos arquivos mesmo se um falhar
        }
      }
    }

    setLoading(false);
    navigate(`/commercial-proposals/template`, {
      state: newState,
    });

    // Registrar auditoria com tratamento de erro melhorado
    try {
      const username = localStorage.getItem("@dsm/username");
      const title = "Criação de Template";
      const description = `${username} criou o template a partir da proposta ${item.name}`;

      const auditResult = await logNewAuditAction(username, title, description);

      if (auditResult.statusCode === 200) {
        console.log('✅ Auditoria registrada:', auditResult.message);
      } else {
        console.warn('⚠️ Falha na auditoria (não crítico):', auditResult.message);
      }
    } catch (error) {
      // Log do erro mas não quebrar a funcionalidade
      console.warn('⚠️ Erro ao registrar auditoria (não crítico):', error.message);
      console.log('🔄 Continuando operação normalmente...');
    }

  } catch (error) {
    console.error('❌ Erro ao criar template:', error);
    message.error("Erro ao criar template. Tente novamente.");
    setLoading(false);
  }
};
