/* Customização do Antd para DSM - Substituindo variáveis LESS */

/* Cores primárias */
:root {
  --primary-color: #00B050;
  --link-color: #00B050;
  --success-color: #00B050;
  --text-color: #404040;
  --border-radius-base: 2px;
}

/* Botões primários */
.ant-btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #009944 !important;
  border-color: #009944 !important;
}

/* Links */
a {
  color: var(--primary-color) !important;
}

a:hover {
  color: #009944 !important;
}

/* Paginação */
.ant-pagination-item-active {
  background-color: white !important;
  border-color: var(--primary-color) !important;
}

.ant-pagination-item-active a {
  color: var(--primary-color) !important;
  font-weight: 400 !important;
}

.ant-pagination-item:hover {
  border-color: var(--primary-color) !important;
}

.ant-pagination-item:hover a {
  color: var(--primary-color) !important;
}

.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* Menu lateral - Removido para evitar conflitos com SideMenu.css específico */

/* Inputs e formulários */
.ant-input:focus,
.ant-input-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(0, 176, 80, 0.2) !important;
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: var(--primary-color) !important;
}

.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(0, 176, 80, 0.2) !important;
}

/* Checkbox e Radio */
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-radio-checked .ant-radio-inner {
  border-color: var(--primary-color) !important;
}

.ant-radio-checked .ant-radio-inner::after {
  background-color: var(--primary-color) !important;
}

/* Switch */
.ant-switch-checked {
  background-color: var(--primary-color) !important;
}

/* Progress */
.ant-progress-bg {
  background-color: var(--primary-color) !important;
}

/* Tabs */
.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--primary-color) !important;
}

.ant-tabs-ink-bar {
  background-color: var(--primary-color) !important;
}

/* Steps */
.ant-steps-item-finish .ant-steps-item-icon {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.ant-steps-item-active .ant-steps-item-icon {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* Spin */
.ant-spin-dot-item {
  background-color: var(--primary-color) !important;
}

/* Rate */
.ant-rate-star-full .ant-rate-star-second {
  color: var(--primary-color) !important;
}

/* Slider */
.ant-slider-track {
  background-color: var(--primary-color) !important;
}

.ant-slider-handle {
  border-color: var(--primary-color) !important;
}

.ant-slider-handle:hover {
  border-color: var(--primary-color) !important;
}

.ant-slider-handle:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 5px rgba(0, 176, 80, 0.2) !important;
}

/* DatePicker */
.ant-picker:hover,
.ant-picker-focused {
  border-color: var(--primary-color) !important;
}

.ant-picker-focused {
  box-shadow: 0 0 0 2px rgba(0, 176, 80, 0.2) !important;
}

/* Fix para DatePicker dropdown z-index */
.ant-picker-dropdown {
  z-index: 9999 !important;
}

/* Fix para RangePicker dropdown */
.ant-picker-range-dropdown {
  z-index: 9999 !important;
}

/* Fix para calendário do DatePicker */
.ant-picker-panel-container {
  z-index: 9999 !important;
}

/* Table */
.ant-table-thead > tr > th {
  background-color: #f5f5f5 !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: rgba(0, 176, 80, 0.05) !important;
}

/* Modal */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0 !important;
}

/* Notification */
.ant-notification-notice-success {
  border-left: 4px solid var(--primary-color) !important;
}

/* Message */
.ant-message-success .anticon {
  color: var(--primary-color) !important;
}

/* Badge */
.ant-badge-status-success {
  background-color: var(--primary-color) !important;
}

/* Timeline */
.ant-timeline-item-head-blue {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* Anchor */
.ant-anchor-link-active > .ant-anchor-link-title {
  color: var(--primary-color) !important;
}

/* BackTop */
.ant-back-top {
  background-color: var(--primary-color) !important;
}

/* Customizações específicas do DSM */
.dsm-header {
  background-color: white !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.dsm-sidebar {
  background-color: #001529 !important;
}

.dsm-sidebar .ant-menu-item-selected {
  background-color: var(--primary-color) !important;
}

.dsm-content {
  background-color: #f5f5f5 !important;
  min-height: calc(100vh - 64px) !important;
}

/* Responsividade */
@media (max-width: 768px) {
  .dsm-sidebar {
    position: fixed !important;
    z-index: 1000 !important;
  }
}

/* Animações suaves - Removido para evitar conflitos com componentes específicos */

/* Correções de bordas */
.ant-btn,
.ant-input,
.ant-select-selector {
  border-radius: var(--border-radius-base) !important;
}

/* Cores de texto */
.ant-typography {
  color: var(--text-color) !important;
}

/* Loading states */
.ant-spin-container {
  position: relative !important;
}

.ant-spin-blur {
  opacity: 0.5 !important;
  pointer-events: none !important;
}

/* Customizações finais */
.ant-layout-sider-trigger {
  background-color: #002140 !important;
}

.ant-layout-sider-trigger:hover {
  background-color: var(--primary-color) !important;
}
