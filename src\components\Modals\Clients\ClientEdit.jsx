import { useState } from "react";
import { MaskedInput } from "antd-mask-input";
import { EditOutlined } from "@ant-design/icons";
import { otrsPut } from "../../../service/apiOtrs";
import { dynamoPost, dynamoPut } from "../../../service/apiDsmDynamo";
import { Button, Col, Input, Modal, Row, Form, message, Tooltip } from "antd";
import Axios from "axios";
import { format } from "date-fns";

export const ClientEditModal = (props) => {
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState();
  const { mutate, clients, client, state, functions, forceTableUpdate, clearSearchAndUpdate, updateClientDataLocally, currentSearch } = props;
  const [edit, setEdit] = useState({});
  const [form] = Form.useForm();
  const fieldEdit = [];

  // Function to check if edited client matches current search filter
  const clientMatchesSearch = (clientData, searchTerm) => {
    if (!searchTerm || searchTerm.trim() === '') return true;

    const searchLower = searchTerm.toLowerCase();

    // Check various client fields that might match the search
    const fieldsToCheck = [
      clientData.fantasy_name,
      clientData.name,
      clientData.cnpj,
      client?.identifications?.itsm_id?.toString(),
      client?.dsm_id?.toString(),
      client?.names?.name,
      client?.names?.fantasy_name
    ];

    return fieldsToCheck.some(field =>
      field && field.toString().toLowerCase().includes(searchLower)
    );
  };

  const handleGetCustomersByState = async () => {
    if (!functions) {
      return;
    }

    try {
      if (state === "todos") {
        const promises = [];

        if (functions.getActiveCustomersWithActiveContract) {
          promises.push(functions.getActiveCustomersWithActiveContract());
        }
        if (functions.getActiveCustomersWithInactiveContract) {
          promises.push(functions.getActiveCustomersWithInactiveContract());
        }
        if (functions.getInactiveCustomers) {
          promises.push(functions.getInactiveCustomers());
        }
        if (functions.getProspects) {
          promises.push(functions.getProspects());
        }

        await Promise.all(promises);

        // Atualizar allCustomers após todas as listas individuais
        if (functions.formatAllCustomers) {
          functions.formatAllCustomers();
        }

      } else {
        switch (state) {
          case "ativosI":
            if (functions.getActiveCustomersWithInactiveContract) {
              await functions.getActiveCustomersWithInactiveContract();
            }
            break;
          case "inativosC":
            if (functions.getInactiveCustomers) {
              await functions.getInactiveCustomers();
            }
            break;
          case "prospect":
            if (functions.getProspects) {
              await functions.getProspects();
            }
            break;
          default: // "ativosA"
            if (functions.getActiveCustomersWithActiveContract) {
              await functions.getActiveCustomersWithActiveContract();
            }
            break;
        }
      }
    } catch (error) {
      console.error('❌ ClientEditModal: Erro ao atualizar lista de clientes:', error);
    }
  };



  async function getCNPJ(cnpj) {
    const transport = Axios.create({
      baseURL: "https://publica.cnpj.ws/cnpj/",
    });

    const { data } = await transport.get(cnpj.replace(/\D/g, ""));

    return data;
  }

  const getCustomer = async () => {
    console.log('🎯 ClientEditModal: Carregando dados do cliente (atualizados do Redux)...', {
      clientId: client?.id,
      clientData: client
    });

    form.resetFields();

    // Verificar se temos dados básicos do cliente
    if (!client || !client.id) {
      console.error('❌ ClientEditModal: Dados do cliente não disponíveis:', client);
      message.error('Erro: Dados do cliente não disponíveis');
      return;
    }

    // Usar dados do prop (que agora são sempre atualizados do Redux)
    console.log('🎯 ClientEditModal: Usando dados atualizados do Redux via prop...');

    const editData = {
      itsm_id: client?.identifications?.itsm_id,
      fantasy_name: client?.names?.fantasy_name,
      crm_id: client?.identifications?.crm_id,
      name: client?.names?.name,
      cnpj: client?.cnpj,
      id: client?.id,
    };

    setEdit(editData);
    form.setFieldsValue(editData);

    console.log('✅ ClientEditModal: Dados definidos no formulário:', editData);
  };

  const editCustomer = async (data) => {
    setLoading(true);

    try {
      let cnpj;

      try {
        cnpj = await getCNPJ(data.cnpj);
      } catch (error) {
        setLoading(false);
        return message.error(
          "Este CNPJ é invalido, confira os dígitos e tente novamente..."
        );
      }

      const {
        natureza_juridica,
        estabelecimento,
        capital_social,
        razao_social,
        socios,
        porte,
      } = cnpj;

      const otrs_put = {
        name: data.fantasy_name,
        razaosocial: razao_social,
        street: `${estabelecimento.tipo_logradouro} ${
          estabelecimento.logradouro
        }, ${estabelecimento.bairro}${
          estabelecimento.complemento ? " " + estabelecimento.complemento : ""
        }, ${estabelecimento.numero}`,
        country: estabelecimento.pais.nome,
        city: estabelecimento.cidade.nome,
        zip: estabelecimento.cep,
        cnpj: data.cnpj,
      };

      try {
        await otrsPut("update/customer/" + data?.itsm_id, otrs_put);
      } catch (error) {
        setLoading(false);
        console.log(error);
        return message.error(
          "Ocorreu um erro ao tentar atualizar o cliente no OTRS :("
        );
      }

      const { bairro, cep, complemento, logradouro, numero, tipo_logradouro } =
        estabelecimento;

      const submit_data = {
        identifications: {
          crm_id: data.crm_id,
          itsm_id: data.itsm_id,
        },
        social_capital: capital_social,
        legal_nature: natureza_juridica,
        names: {
          fantasy_name: data.fantasy_name,
          name: razao_social,
        },
        phones: {
          ddd1: estabelecimento.ddd1,
          telefone1: estabelecimento.telefone1,
          ddd2: estabelecimento.ddd2,
          telefone2: estabelecimento.telefone2,
          ddd_fax: estabelecimento.ddd_fax,
          fax: estabelecimento.fax,
        },
        cnpj: data.cnpj,
        members: socios,
        address: {
          cep,
          bairro,
          numero,
          logradouro,
          complemento,
          tipo_logradouro,
          pais: estabelecimento.pais.nome,
          cidade: estabelecimento.cidade.nome,
        },
        extent: porte,
        active: client.active,
        contacts: client.contacts,
        accounts: client.accounts,
        updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
      };

      try {
        await dynamoPut(
          `${process.env.REACT_APP_STAGE}-customers`,
          client.id,
          submit_data
        );

        console.log('🎯 ClientEditModal: Cliente salvo, atualizando dados localmente...');

        // Update client data locally (optimistic update)
        if (updateClientDataLocally) {
          updateClientDataLocally(client.id, submit_data);
          console.log('✅ ClientEditModal: Dados do cliente atualizados localmente');
        } else {
          console.log('⚠️ ClientEditModal: Função de atualização local não disponível, usando refresh...');
          await handleGetCustomersByState();
        }

        const hasActiveSearch = currentSearch && currentSearch.trim() !== '';

        if (hasActiveSearch) {
          const editedClientData = form.getFieldsValue();
          const clientWillBeVisible = clientMatchesSearch(editedClientData, currentSearch);

          if (!clientWillBeVisible) {
            if (clearSearchAndUpdate) {
              clearSearchAndUpdate();
            }
          } else {
            if (forceTableUpdate) {
              forceTableUpdate();
            }
          }
        } else {
          if (forceTableUpdate) {
            forceTableUpdate();

            setTimeout(() => {
              forceTableUpdate();
            }, 100);
          }
        }
      } catch (error) {
        setLoading(false);
        console.log(error);
        return message.error(
          "O cliente foi atualizado no OTRS, mas ocorreu um erro ao tentar atualizar o cliente no DSM :("
        );
      }

      const username = localStorage.getItem("@dsm/username");

      function uniqByKeepLast(data, key) {
        return [...new Map(data.map((x) => [key(x), x]).values())];
      }

      let arr = [];

      const newArr = uniqByKeepLast(fieldEdit, (e) => e.split(" ")[0]);

      newArr.forEach((e) => {
        return arr.push(e[1]);
      });

      const title = "Cliente Editado";

      const description = `${username} editou o cliente ${
        data.fantasy_name
      }, com as seguintes alterações: ${arr.join(" ")}.`;

      dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
        username: username,
        name: title,
        description: description,
        created_at: new Date(),
        updated_at: new Date(),
      });

      setShowModal(false);
      message.success("Cliente atualizado com sucesso!");
    } catch (err) {
      console.log(err);
      message.error(
        "Ops! Ocorreu um erro inesperado ao tentar atualizar este cliente..."
      );
    }
    setLoading(false);
  };

  function getChange(key) {
    for (let i = 0; i < Object.entries(edit).length; i++) {
      if (Object.entries(edit)[i][0] === key) {
        return Object.values(edit)[i];
      }
    }
  }

  return (
    <>
      <Tooltip title="Editar informações do cliente">
        <Button
          style={{
            cursor: "pointer",
            backgroundColor: "transparent",
            border: "none",
          }}
          onClick={async () => {
            setShowModal(true);
            await getCustomer();
          }}
        >
          <EditOutlined />
        </Button>
      </Tooltip>
      <Modal
        title="Editar Cliente"
        open={showModal}
        onCancel={() => setShowModal(false)}
        closable={false}
        footer={[
          <Button onClick={() => setShowModal(false)}>Cancelar</Button>,
          <Button
            loading={loading}
            type="primary"
            onClick={() => form.submit()}
          >
            Salvar
          </Button>,
        ]}
      >
        <Row align="middle" justify="center">
          <Col span={24}>
            <Form
              onValuesChange={(data) => {
                fieldEdit.push(
                  `${Object.keys(data)[0]} alterado de: ${getChange(
                    Object.keys(data)[0]
                  )} para ${Object.values(data)[0]}`
                );
              }}
              form={form}
              onFinish={editCustomer}
              name="control-hooks"
              layout="vertical"
              requiredMark={false}
            >
              <Form.Item hidden name="id" />
              <Form.Item
                rules={[{ required: true, message: "Preencha este campo." }]}
                label="ITSM ID"
                name="itsm_id"
              >
                <Input placeholder="ITSM ID" />
              </Form.Item>
              <Form.Item label="CRM ID" name="crm_id">
                <Input placeholder="CRM ID" />
              </Form.Item>
              <Form.Item
                rules={[{ required: true, message: "Preencha este campo." }]}
                label="Nome Fantasia"
                name="fantasy_name"
              >
                <Input placeholder="Nome Fantasia" />
              </Form.Item>
              <Form.Item
                rules={[{ required: true, message: "Preencha este campo." }]}
                label="CNPJ"
                name="cnpj"
              >
                <MaskedInput mask="00.000.000/0000-00" placeholder="CNPJ" />
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
