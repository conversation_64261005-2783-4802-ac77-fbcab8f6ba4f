import {
  Modal,
  Button,
  Select,
  Form,
  message,
  Row,
  Col,
  Table,
  Space,
  Popconfirm,
  Tag,
  Tooltip,
} from "antd";
import React, { useState } from "react";
import { DeleteOutlined, UserOutlined } from "@ant-design/icons";
import { dynamoPost, dynamoPut, dynamoGetById } from "../../../service/apiDsmDynamo";
import { v4 } from "uuid";
import useSWR from "swr";
import Axios from "axios";
import { CreateExecutive } from "./CreateExecutive";
import { CreateExternalExecutive } from "./CreateExternalExecutive";
import { VisualizeExecutiveInfo } from "./VisualizeExecutiveInfo";
import { getExecutiveNameFromEmail } from "../../../utils/getExecutiveNameFromEmail";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import { changeExecutiveStatus } from "./controllers/changeActiveExecutive";
import { shallowEqual, useSelector } from "react-redux";

export const ExecutivesModal = (props) => {
  const {
    executives,
    getCustomerContracts,
    contract,
    permissions,
    executivesData,
  } = props;
  const [loadingExecutives, setLoadingExecutives] = useState(false);
  const [loadingRequest, setLoadingRequest] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showCreateExecutiveModal, setShowCreateExecutiveModal] =
    useState(false);
  const [hidden, setHidden] = useState(true);
  const [show, setShow] = useState(false);
  const [form] = Form.useForm();
  const { Option } = Select;
  const { users } = useSelector((state) => state.contract, shallowEqual);
  
  const handleRemove = async (data) => {
    setLoadingExecutives(true);
    let contractCopy = JSON.parse(JSON.stringify(contract));
    try {
      contractCopy["executives"] = contract["executives"].filter(
        (executive) => executive.id !== data.id
      );

      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-contracts`,
        contractCopy.id,
        contractCopy
      );

      console.log('🔄 ExecutivesModal: Atualizando lista de contratos após remoção...');
      await getCustomerContracts();

      message.success("Executivo removido com sucesso");

      const username = localStorage.getItem("@dsm/username");
      const title = "Executivo Removido";
      const executiveName = await getExecutiveNameFromEmail(data.email);
      const description = `${username} removeu o executivo ${executiveName} do contrato ${contractCopy.name} com ID ${contractCopy.identifications.itsm_id}.`;

      logNewAuditAction(username, title, description);

      setLoadingExecutives(false);
    } catch (err) {
      message.error("Erro ao tentar remover executivo");
      setLoadingExecutives(false);
    }
  };

  const closeModal = () => {
    setShowModal(false);
    form.resetFields();
  };

  const handleModal = (e) => {
    if (e === "outros") {
      setShowCreateExecutiveModal(!showCreateExecutiveModal);
      form.resetFields();
    } else {
      console.log('🎯 ExecutivesModal: Executivo interno selecionado:', e);
      // Verificar se é um JSON válido
      try {
        const parsed = JSON.parse(e);
        console.log('✅ ExecutivesModal: JSON do executivo válido:', parsed);
      } catch (error) {
        console.error('❌ ExecutivesModal: JSON do executivo inválido:', error);
      }
    }
  };
  const handleSubmit = async (data, isExternal) => {
    if (isExternal) {
      // Para executivos externos, verificar se executive existe e tem os campos necessários
      if (!data.executive || !data.executive.user || !data.executive.email || !data.executive.type) {
        console.error('❌ ExecutivesModal: Dados incompletos para executivo externo:', data.executive);
        return message.error("Preencha todos os campos obrigatórios");
      }
      console.log('✅ ExecutivesModal: Validação passou para executivo externo');
    } else {
      // Para executivos internos, verificar se executive e type existem
      if (!data.executive || !data.type) {
        console.error('❌ ExecutivesModal: Dados incompletos para executivo interno:', {
          executive: data.executive,
          type: data.type,
          executiveType: typeof data.executive,
          typeType: typeof data.type
        });
        return message.error("Preencha todos os campos");
      }

      // Para executivos internos, data.executive é uma string JSON
      try {
        const parsedExecutive = JSON.parse(data.executive);
        console.log('✅ ExecutivesModal: Validação passou para executivo interno:', {
          parsedExecutive,
          type: data.type
        });
      } catch (parseError) {
        console.error('❌ ExecutivesModal: Erro ao fazer parse do executivo interno:', {
          executive: data.executive,
          error: parseError.message
        });
        return message.error("Dados do executivo inválidos");
      }
    }

    console.log('✅ ExecutivesModal: Validação passou, processando executivo...');
    setLoadingRequest(true);
    setLoadingExecutives(true);

    let contractCopy = JSON.parse(JSON.stringify(contract));
    const username = localStorage.getItem("@dsm/username");

    let submit = isExternal ? data.executive : JSON.parse(data.executive);

    submit["type"] = isExternal
      ? `Externo - ${data.executive.type}`
      : data.type;
    submit["active"] = 1;
    submit["id"] = v4();
    submit["phone"] = isExternal ? data.executive.phone : "";
    submit["customer_itsm"] = parseInt(contract.identifications.itsm_id);

    try {
      if (Array.isArray(contract.executives)) {
        contractCopy["executives"].push(submit);
        console.log('🎯 ExecutivesModal: Executivo adicionado ao array existente');
      } else {
        contractCopy["executives"] = [submit];
        console.log('🎯 ExecutivesModal: Criado novo array de executivos');
      }

      console.log('🎯 ExecutivesModal: Contrato final antes do salvamento:', {
        contractId: contractCopy.id,
        totalExecutives: contractCopy.executives?.length || 0,
        executivesList: contractCopy.executives?.map(e => ({
          id: e.id,
          user: e.user,
          email: e.email,
          type: e.type
        })) || []
      });

      console.log('🔄 ExecutivesModal: Salvando contrato no DynamoDB...');
      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-contracts`,
        contractCopy.id,
        contractCopy
      );
      console.log('✅ ExecutivesModal: Contrato salvo no DynamoDB com sucesso');

      const title = "Executivo adicionado";

      const description = `${username} adicionou o executivo ${
        isExternal ? data.executive.user : JSON.parse(data.executive).user
      } do contrato ${contractCopy.name}, com ID ${
        contractCopy.identifications.itsm_id
      }.`;

      const executiveName = isExternal ? data.executive.user : submit.user;

      console.log('✅ ExecutivesModal: Executivo salvo no banco com sucesso:', {
        executiveName,
        contractName: contractCopy.name,
        contractId: contractCopy.identifications.itsm_id,
        executiveId: submit.id
      });

      logNewAuditAction(username, title, description);

      console.log('🔄 ExecutivesModal: Iniciando atualização da lista de contratos...');

      // Aguardar atualização real do Redux
      await getCustomerContracts();
      console.log('✅ ExecutivesModal: getCustomerContracts() concluído');

      // Aguardar um pouco para garantir consistência do banco
      console.log('⏳ ExecutivesModal: Aguardando consistência do banco (2s)...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Forçar nova busca dos contratos para garantir dados atualizados
      console.log('🔄 ExecutivesModal: Forçando nova busca dos contratos...');
      await getCustomerContracts();
      console.log('✅ ExecutivesModal: Segunda busca concluída');

      // Verificar se o executivo foi realmente adicionado (busca direta no banco)
      console.log('🔍 ExecutivesModal: Verificando se executivo foi adicionado...');
      try {
        const tableName = `${process.env.REACT_APP_STAGE}-contracts`;
        const updatedContract = await dynamoGetById(tableName, contract.id);

        const executiveExists = updatedContract?.executives?.some(exec =>
          exec.id === submit.id || exec.email === submit.email
        );

        console.log('🎯 ExecutivesModal: Verificação do executivo:', {
          executiveExists,
          totalExecutivesInBank: updatedContract?.executives?.length || 0,
          executiveId: submit.id,
          executiveEmail: submit.email
        });

        if (executiveExists) {
          console.log('✅ ExecutivesModal: Executivo confirmado no banco!');
          message.success(`Executivo ${executiveName} adicionado com sucesso!`);
        } else {
          console.warn('⚠️ ExecutivesModal: Executivo não encontrado no banco após salvamento');
          message.warning(`Executivo ${executiveName} foi salvo, mas pode demorar para aparecer na lista.`);
        }
      } catch (verifyError) {
        console.error('❌ ExecutivesModal: Erro ao verificar executivo:', verifyError);
        message.success(`Executivo ${executiveName} adicionado com sucesso!`);
      }

      console.log('🎯 ExecutivesModal: Executivo deve aparecer na próxima abertura da modal');

      form.resetFields();
      setHidden(true);
      setLoadingRequest(false);
      setLoadingExecutives(false);
    } catch (err) {
      console.error('❌ ExecutivesModal: Erro ao adicionar executivo:', {
        error: err.message,
        stack: err.stack,
        executiveData: submit
      });

      message.error("Erro na adição do executivo: " + err.message);
      setLoadingRequest(false);
      setLoadingExecutives(false);
    }
  };

  const handleChangeExecutiveStatus = async (data) => {
    setLoadingExecutives(true);
    await changeExecutiveStatus(data, contract);
    await getCustomerContracts();
    setLoadingExecutives(false);
  };

  const columns = [
    {
      code: "view_executive_contract_name",
      dataIndex: "user",
      title: "Nome",
      render: (user, item) => {
        if (user && user !== "" && !user.includes("undefined")) {
          return user;
        }
        // Para executivos internos, extrair nome do email
        if (item.email && !item.type?.includes("Externo")) {
          return getExecutiveNameFromEmail(item.email);
        }
        return "-";
      },
    },
    {
      code: "view_executive_contract_email",
      dataIndex: "email",
      title: "Email",
    },
    {
      code: "view_executive_contract_phone",
      dataIndex: "phone",
      title: "Telefone",
      render: (phone) => {
        return phone && phone !== "" ? phone : "-";
      },
    },
    {
      code: "view_executive_contract_type",
      dataIndex: "type",
      title: "Tipo",
    },
    {
      code: "view_executive_contract_active",
      title: "Ativo",
      dataIndex: "active",
      key: "active",
      width: "1%",
      render: (a) => {
        if (a === 1) {
          return (
            <Row justify={"center"}>
              <Tag color="green">Ativo</Tag>
            </Row>
          );
        } else {
          return (
            <Row justify={"center"}>
              <Tag color="red">Inativo</Tag>
            </Row>
          );
        }
      },
      sorter: (a, b) => a.active - b.active,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_info",
      title: "Informações",
      dataIndex: "id",
      width: "1%",
      render: (field, item) => {
        return (
          <Row justify="center">
            <Col>
              <VisualizeExecutiveInfo executive={item} />
            </Col>
          </Row>
        );
      },
    },
    {
      code: "view_executive_contract_actions",
      title: "Ações",
      dataIndex: "active",
      key: "active",
      align: "center",
      width: "1%",
      render: (active, item) => (
        <BtnChangeExecutiveStatus
          item={item}
          active={active}
          handleChangeExecutiveStatus={handleChangeExecutiveStatus}
        />
      ),
    },
    {
      code: "view_executive_contract_remove",
      dataIndex: "id",
      title: "Remover",
      width: "1%",
      render: (field, item) => {
        return (
          <Tooltip title="Remover executivo deste contrato">
            <Popconfirm
              title="Tem certeza que deseja remover este executivo?"
              okText="Sim"
              cancelText="Não"
              onConfirm={() => handleRemove(item)}
            >
              <Button type="danger">
                <DeleteOutlined />
              </Button>
            </Popconfirm>
          </Tooltip>
        );
      },
    },
  ];

  return (
    <>
      <CreateExecutive
        setShow={(state) => setShow(state)}
        executives={executivesData}
        form={form}
        show={show}
      />
      <Tooltip title="Visualize e altere os executivos deste contrato">
        <Button
          style={{
            color: contract?.executives?.length ? "#0f9347" : "#333",
          }}
          type="text"
          onClick={async () => {
            setShowModal(true);
          }}
        >
          <UserOutlined />
        </Button>
      </Tooltip>
      <Modal
        width="60vw"
        title="Executivos"
        onCancel={() => {
          form.resetFields();
          setShowModal(false);
        }}
        open={showModal}
        closable={false}
        footer={[
          <Button key="close-executives" type="primary" onClick={closeModal}>
            Fechar
          </Button>,
        ]}
      >
        <Row align="middle" justify="center">
          <Col span={24}>
            {permissions
              ?.map((permission) => {
                return permission.code;
              })
              .includes("view_executive_contract_add") ? (
              hidden === true ? (
                <Row
                  style={{ marginBottom: "2em" }}
                  justify="center"
                  align="middle"
                >
                  <Col>
                    <Button
                      type="primary"
                      onClick={() => {
                        setHidden(false);
                        form.setFieldsValue({ contract_id: contract.id });
                      }}
                    >
                      Adicionar Executivo
                    </Button>
                  </Col>
                </Row>
              ) : (
                <Form
                  form={form}
                  onFinish={handleSubmit}
                  name="control-hooks"
                  layout="vertical"
                >
                  <Form.Item hidden name="contract_id" />

                  <Row gutter={[24, 24]}>
                    <Col span={12}>
                      <Form.Item name="executive" label="Selecione o executivo">
                        <Select
                          showSearch
                          placeholder={
                            !users || users.length === 0
                              ? "Carregando usuários..."
                              : "Selecione..."
                          }
                          optionFilterProp="children"
                          onChange={(e) => handleModal(e)}
                          loading={!users || users.length === 0}
                          filterOption={(input, option) =>
                            option.children
                              ?.toLowerCase()
                              .indexOf(input?.toLowerCase()) >= 0
                          }
                        >
                          <Option value={"outros"} key={"outros"}>
                            outros
                          </Option>
                          {users && users.length > 0 ? (
                            users
                              ?.filter(
                                ({ user }) =>
                                  !contract?.executives
                                    ?.map((c) => {
                                      return c.user;
                                    })
                                    .includes(user)
                              )
                              .map((e, i) => {
                                console.log('🎯 ExecutivesModal: Renderizando usuário no Select:', {
                                  email: e.email,
                                  user: e.user,
                                  index: i
                                });
                                return (
                                  <Option
                                    value={JSON.stringify({
                                      email: e.email,
                                      user: e.user,
                                    })}
                                    key={i}
                                  >
                                    {e.email}
                                  </Option>
                                );
                              })
                          ) : (
                            <Option disabled key="no-users">
                              {users === null ? "Carregando usuários..." : "Nenhum usuário disponível"}
                            </Option>
                          )}
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item name="type" label="Selecione o tipo">
                        <Select
                          showSearch
                          placeholder="Selecione..."
                          optionFilterProp="children"
                          onChange={(e) => {
                            if (e === "Outro") {
                              setShow(true);
                            }
                          }}
                          filterOption={(input, option) =>
                            option.children
                              ?.toLowerCase()
                              .indexOf(input?.toLowerCase()) >= 0
                          }
                        >
                          {[...executivesData?.data]
                            ?.sort((a, b) => a?.name?.localeCompare(b?.name))
                            ?.map((executive) => (
                              <Select.Option value={executive?.name}>
                                {executive?.name}
                              </Select.Option>
                            ))}
                          <Select.Option value="Outro">Outro</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Space align="start">
                    <Form.Item>
                      <Button
                        loading={loadingRequest}
                        type="primary"
                        htmlType="submit"
                      >
                        Adicionar
                      </Button>
                    </Form.Item>
                    <Button
                      type="primary"
                      onClick={() => {
                        form.resetFields();
                        setHidden(true);
                      }}
                    >
                      Cancelar
                    </Button>
                  </Space>
                </Form>
              )
            ) : null}
          </Col>
        </Row>

        <Table
          scroll={{ x: "100%" }}
          dataSource={executives}
          columns={columns.filter((e) =>
            permissions
              .map((permission) => {
                return permission.code;
              })
              .includes(e.code)
          )}
          loading={loadingExecutives}
        />
      </Modal>
      <CreateExternalExecutive
        open={showCreateExecutiveModal}
        executives={executives}
        setModal={setShowCreateExecutiveModal}
        handleSubmit={handleSubmit}
        contractId={contract.id}
      />
    </>
  );
};

const BtnChangeExecutiveStatus = ({
  item,
  active,
  handleChangeExecutiveStatus,
}) => {
  return (
    <Row justify={"center"}>
      <Popconfirm
        placement="leftBottom"
        title={`Tem certeza que deseja ${
          active === 1 ? "desativar" : "ativar"
        } este executivo?`}
        style={{
          backgroundColor: "transparent",
          border: "none",
          cursor: "pointer",
          color: "black",
        }}
        onConfirm={async () => await handleChangeExecutiveStatus(item)}
        cancelText="Cancelar"
      >
        <Button style={{ padding: "0" }} type="text">
          {active === 1 ? (
            <Tag color="red">Desativar</Tag>
          ) : (
            <Tag color="green">Ativar</Tag>
          )}
        </Button>
      </Popconfirm>
    </Row>
  );
};
