import { Table, Typography, Empty, ConfigProvider } from "antd";

export function DynamicTable({
  data,
  columns,
  scroll = { x: "100%" },
  loading = false,
  ...rest
}) {
  const { Text } = Typography;

  

  return (
    <ConfigProvider
      renderEmpty={() =>
        loading ? (
          <Text>Carregando...</Text>
        ) : (
          <Empty description={"Sem dados para a pesquisa"} />
        )
      }
    >
      <Table
        scroll={scroll}
        columns={columns}
        dataSource={data}
        pagination={true}
        loading={loading}
        {...rest}
      />
    </ConfigProvider>
  );
}
