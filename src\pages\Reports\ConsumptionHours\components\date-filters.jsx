import { Col, DatePicker, Typography } from "antd";
import { shallowEqual, useSelector } from "react-redux";
import { setConsumptionState } from "../../../../store/actions/consumption-hours-action";
import dayjs from "dayjs";

export const DateFilters = () => {

    return (
        <>
            <DateField
                label={"Inicio:"}
                selector={"dtStart"}
                offset={1}
            />

            <DateField
                label={"Fim:"}
                selector={"dtEnd"}
                offset={1}
            />
        </>
    )
}

const DateField = ({ label, selector, offset = 0 }) => {
    const state = useSelector(state => state.consumption[selector], shallowEqual);

    return (
        <Col
            xl={{ span: 3, offset  }}
            lg={{ span: 6, offset }}
            md={{ span: 3, offset }}
            sm={{ span: 10, offset }}
            style={{ marginBottom: 10 }}
        >
            <Typography.Text>{label}</Typography.Text>
            <DatePicker
                placeholder="Selecione um mês"
                picker="month"
                style={{ width: "100%" }}
                value={state ? dayjs(state) : null} // Converter string de volta para dayjs para exibição
                onChange={(momentObj) => {
                    // Converter para string antes de salvar no Redux
                    const value = momentObj ? momentObj.format('YYYY-MM-DD') : null;
                    setConsumptionState({ field: selector, value });
                }}
            />
        </Col>
    )
}