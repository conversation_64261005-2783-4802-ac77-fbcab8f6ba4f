import React from "react";
import { DatePicker } from "antd";
import moment from "moment";
import dayjs from "dayjs";
import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

export const RangePickerStandard = ({
  onChange,
  value,
  placeholder = ["Selecione um período", "Selecione um período"],
}) => {
  const format = "DD/MM/YYYY";
  const { RangePicker } = DatePicker;

  let firstDayOfCurrentMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    1
  );
  let lastDayOfCurrentMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth() + 1,
    0
  );
 
  return (
    <RangePicker
      style={{ width: "100%" }}
      locale={locale}
      onChange={onChange}
      defaultValue={[
        dayjs(firstDayOfCurrentMonth),
        dayjs(lastDayOfCurrentMonth),
      ]}
      value={
        value.length === 0
          ? [
              dayjs(firstDayOfCurrentMonth),
              dayjs(lastDayOfCurrentMonth),
            ]
          : [dayjs(value[0], format), dayjs(value[1], format)]
      }
      allowClear={false}
      placeholder={placeholder}
      format={format}
      disabledDate={(current) => {
        // Limitar seleção entre 2 anos atrás e a data atual
        const twoYearsAgo = dayjs().subtract(2, 'years').startOf('day');
        const today = dayjs().endOf('day');
        return current && (current.isBefore(twoYearsAgo) || current.isAfter(today));
      }}
    />
  );
};
