import { Form, Row, Modal, Input, Col, message } from "antd";
import { maskPhone } from "../../../utils/masks";
import { useState } from "react";

export const CreateExternalExecutive = ({
  open,
  setModal,
  handleSubmit,
  contractId,
  executives,
}) => {
  const [form] = Form.useForm();
  const [phone, setPhone] = useState("");

  function clear() {
    form.resetFields();
  }

  function onCancel() {
    setModal(false);
    clear();
  }

  function onFilterFinish(values) {
    if (!values.firstName || !values.lastName) {
      return message.error("Preencha o nome completo");
    }

    if (!values.email) {
      return message.error("Preencha o email");
    }

    if (!values.role) {
      return message.error("Preencha o cargo");
    }

    if (!phone || phone.length < 14) {
      return message.error("Preencha um telefone válido");
    }

    const executiveAlreadyExists = executives.find(
      (e) => e.email === values.email
    );

    if (executiveAlreadyExists) {
      message.error("Executivo já adicionado");
      return false;
    }

    const formatedValues = {
      user: `${values.firstName} ${values.lastName}`,
      email: values.email,
      phone: phone,
      type: values.role,
    };

    handleSubmit({ contract_id: contractId, executive: formatedValues }, true);
    setModal(false);
    setPhone("");
    clear();
  }
  return (
    <Modal
      open={open}
      onCancel={onCancel}
      onOk={form.submit}
      title="Criar Contato"
      centered
      width={450}
      closable={false}
      cancelText="Cancelar"
    >
      <Row>
        <Col span={24}>
          <Form
            form={form}
            style={{ display: "flex", flexDirection: "column" }}
            name="basic"
            initialValues={{
              remember: true,
            }}
            onFinish={onFilterFinish}
            layout={"vertical"}
            autoComplete="off"
          >
            <Form.Item
              name="firstName"
              label="Primeiro Nome"
              rules={[
                { required: true, message: "Campo obrigatório" },
                {
                  pattern: /^[a-zA-ZÀ-ÿ\s]+$/,
                  message: "Digite um nome válido",
                },
              ]}
            >
              <Input
                style={{ width: "90%", margin: "5px" }}
                placeholder="Primeiro Nome..."
              ></Input>
            </Form.Item>
            <Form.Item
              name="lastName"
              label="Último Nome"
              rules={[
                { required: true, message: "Campo obrigatório" },
                {
                  pattern: /^[a-zA-ZÀ-ÿ\s]+$/,
                  message: "Digite um nome válido",
                },
              ]}
            >
              <Input
                style={{ width: "90%", margin: "5px" }}
                placeholder="Último Nome..."
              ></Input>
            </Form.Item>

            <Form.Item
              name="role"
              label="Cargo"
              rules={[{ required: true, message: "Campo obrigatório" }]}
            >
              <Input
                style={{ width: "90%", margin: "5px" }}
                placeholder="Cargo..."
              ></Input>
            </Form.Item>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: "Campo obrigatório" },
                {
                  pattern:
                    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                  message: "Digite um email válido",
                },
              ]}
            >
              <Input
                type="email"
                style={{ width: "90%", margin: "5px" }}
                placeholder="Email..."
              ></Input>
            </Form.Item>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
                Telefone
              </label>
              <Input
                type="text"
                value={phone}
                onChange={(event) =>
                  setPhone(maskPhone(event.currentTarget.value))
                }
                style={{ width: "90%", margin: "5px" }}
                placeholder="(__) _____-____"
                maxLength={15}
              />
            </div>
          </Form>
        </Col>
      </Row>
    </Modal>
  );
};
