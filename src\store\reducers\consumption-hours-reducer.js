import { createSlice } from "@reduxjs/toolkit";
import moment from "moment";
import dayjs from "dayjs";

const REDUCER_NAME = "consumptionHours";

export const COLLUMNS_CONSUMPTION_HOURS = [
  "DSM ID",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "Squad",
  "Horas Contratadas",
  "Horas Expiradas",
  "Horas Consumidas",
  "Saldo",
  "Horas Acumuladas",
  "Horas Excedentes",
  "Valor Hora",
  "Total Excedente",
  "Consumo Médio",
  "Total Horas Expiradas",
];

const allOption = { value: 0, label: "Todos" };

const INITIAL_STATE = {
  search: "",
  dtEnd: dayjs().format('YYYY-MM-DD'), // Converter para string serializável
  dtStart: dayjs().format('YYYY-MM-DD'), // Converter para string serializável
  contracts: [],
  updatedHoursContracts: [],
  squadOptions: [],
  contractTypes: [allOption],
  selectedSquads: [allOption],
  filteredContracts: [],
  selectedStatus: allOption,
  selectedHourType: allOption,
  selectedContract: 0,
  collumnsToShow: COLLUMNS_CONSUMPTION_HOURS,
  collumnsConsumptionHours: COLLUMNS_CONSUMPTION_HOURS,
  pageSelected: 1,
};

const consumptionHoursSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setConsumptionStateReduce(state, action) {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }
      state[action.payload.field] = value;
    },
    setConsumptionSearchStateReduce(state, action) {
      state["contracts"] = action.payload.contracts;
      state["pageSelected"] = action.payload.pageSelected;
    },
    setUpdatedHoursContractsReduce(state, action) {
      state["updatedHoursContracts"] = action.payload.updatedHoursContracts;
    },
  },
});

export const {
  setConsumptionStateReduce,
  setConsumptionSearchStateReduce,
  setUpdatedHoursContractsReduce,
} = consumptionHoursSlice.actions;

export default consumptionHoursSlice.reducer;
