/* ✅ Correções de Layout e Tipografia - DSM Frontend */
/* Arquivo para normalizar diferenças visuais entre versões */

/* ========================================
   NORMALIZAÇÃO DE TIPOGRAFIA
   ======================================== */

/* Peso da fonte padrão para componentes */
.ant-typography,
.ant-typography p,
.ant-typography div,
.ant-typography span {
  font-weight: 400 !important;
}

/* Títulos com peso correto */
.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4,
.ant-typography h5,
.ant-typography h6 {
  font-weight: 600 !important;
}

/* Texto strong apenas quando necessário */
.ant-typography strong,
.ant-typography .ant-typography-strong {
  font-weight: 600 !important;
}

/* ========================================
   NORMALIZAÇÃO DE TAMANHOS
   ======================================== */

/* Tamanho de fonte padrão */
.ant-typography,
.ant-table,
.ant-form,
.ant-input,
.ant-select,
.ant-btn {
  font-size: 14px !important;
  line-height: 1.5714285714285714 !important;
}

/* Botões com tamanho correto */
.ant-btn {
  height: 32px !important;
  padding: 4px 15px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* Inputs com tamanho correto */
.ant-input:not(.ant-input-textarea),
.ant-select-selector {
  height: 32px !important;
  padding: 4px 11px !important;
  font-size: 14px !important;
}

/* TextArea com altura automática baseada em rows */
.ant-input-textarea {
  font-size: 14px !important;
}

/* ========================================
   NORMALIZAÇÃO DE ESPAÇAMENTOS
   ======================================== */

/* Espaçamento de cards */
.ant-card {
  margin-bottom: 16px !important;
}

.ant-card-body {
  padding: 24px !important;
}

/* Espaçamento de formulários */
.ant-form-item {
  margin-bottom: 24px !important;
}

.ant-form-item-label {
  padding-bottom: 8px !important;
}

/* Espaçamento de tabelas */
.ant-table-thead > tr > th {
  padding: 16px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.ant-table-tbody > tr > td {
  padding: 16px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* ========================================
   CORREÇÕES ESPECÍFICAS DE COMPONENTES
   ======================================== */

/* Paginação normalizada */
.ant-pagination {
  font-size: 14px !important;
}

.ant-pagination-item {
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
}

.ant-pagination-item a {
  font-size: 14px !important;
  font-weight: 400 !important;
}

/* Menu lateral normalizado - Exceto sidemenu que tem CSS específico */
.ant-menu:not(.ant-menu-dark) {
  font-size: 14px !important;
}

.ant-menu-item:not(.ant-menu-dark .ant-menu-item) {
  height: 40px !important;
  line-height: 40px !important;
  font-weight: 400 !important;
}

/* Tabs normalizadas */
.ant-tabs-tab {
  padding: 12px 16px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

.ant-tabs-tab-active {
  font-weight: 600 !important;
}

/* Modal normalizado */
.ant-modal-header {
  padding: 16px 24px !important;
}

.ant-modal-body {
  padding: 24px !important;
}

.ant-modal-title {
  font-size: 16px !important;
  font-weight: 600 !important;
}

/* ========================================
   CORREÇÕES DE DENSIDADE VISUAL
   ======================================== */

/* Reduzir densidade se necessário */
.ant-table-small .ant-table-thead > tr > th,
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px !important;
}

/* Espaçamento de listas */
.ant-list-item {
  padding: 12px 0 !important;
}

/* Espaçamento de descriptions */
.ant-descriptions-item-label {
  font-weight: 600 !important;
}

.ant-descriptions-item-content {
  font-weight: 400 !important;
}

/* ========================================
   CORREÇÕES DE BORDAS E RAIOS
   ======================================== */

/* Bordas consistentes */
.ant-btn,
.ant-input,
.ant-select-selector,
.ant-card {
  border-radius: 6px !important;
}

/* ========================================
   CORREÇÕES DE CORES DE TEXTO
   ======================================== */

/* Cor de texto padrão */
.ant-typography,
.ant-table,
.ant-form,
.ant-menu {
  color: #000000d9 !important;
}

/* Cor de texto secundário */
.ant-typography-caption,
.ant-form-item-extra {
  color: #00000073 !important;
}

/* ========================================
   CORREÇÕES ESPECÍFICAS PARA DSM
   ======================================== */

/* Remover negritos desnecessários em tabelas */
.ant-table-thead > tr > th .ant-table-column-title {
  font-weight: 600 !important;
}

.ant-table-tbody > tr > td {
  font-weight: 400 !important;
}

/* Normalizar peso de fonte em cards */
.ant-card-head-title {
  font-weight: 600 !important;
  font-size: 16px !important;
}

/* Normalizar estatísticas */
.ant-statistic-title {
  font-weight: 400 !important;
  font-size: 14px !important;
}

.ant-statistic-content {
  font-weight: 600 !important;
}

/* ========================================
   CORREÇÕES DE RESPONSIVIDADE
   ======================================== */

/* Garantir que os componentes não fiquem muito grandes em telas pequenas */
@media (max-width: 768px) {
  .ant-card-body {
    padding: 16px !important;
  }
  
  .ant-modal-body {
    padding: 16px !important;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px !important;
  }
}

/* ========================================
   RESET DE ESTILOS CONFLITANTES
   ======================================== */

/* Remover estilos que podem estar causando problemas */
* {
  box-sizing: border-box;
}

/* Garantir que não há estilos globais afetando o peso da fonte */
body, html {
  font-weight: 400 !important;
}

/* ========================================
   CORREÇÕES ESPECÍFICAS PARA TEXTAREA - v2.0
   ======================================== */

/* Garantir que TextArea respeite a propriedade rows */
.ant-input-textarea textarea {
  min-height: auto !important;
  height: auto !important;
  resize: vertical !important;
}

/* Garantir que o wrapper do TextArea não tenha altura fixa */
.ant-input-textarea {
  height: auto !important;
  min-height: auto !important;
}

/* Regra mais específica para TextAreas com rows definidos */
.ant-form-item .ant-input-textarea,
.ant-form-item-control .ant-input-textarea {
  height: auto !important;
  min-height: auto !important;
}

/* Garantir que o textarea interno também respeite as regras */
.ant-form-item .ant-input-textarea textarea,
.ant-form-item-control .ant-input-textarea textarea {
  height: auto !important;
  min-height: auto !important;
  resize: vertical !important;
}

/* Regra super específica para forçar o comportamento correto */
textarea.ant-input {
  height: auto !important;
  min-height: auto !important;
}

/* Garantir que TextAreas com atributo rows funcionem */
textarea[rows] {
  height: auto !important;
  min-height: auto !important;
}

/* FORÇA MÁXIMA - Regra com máxima especificidade para TextArea */
.ant-form .ant-form-item .ant-form-item-control .ant-form-item-control-input .ant-form-item-control-input-content .ant-input-textarea,
.ant-form .ant-form-item .ant-form-item-control .ant-form-item-control-input .ant-form-item-control-input-content .ant-input-textarea textarea {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* Regra específica para o componente TextArea do Ant Design v5 */
.ant-input-textarea-show-count::after {
  height: auto !important;
}

/* SOLUÇÃO DEFINITIVA - Força o comportamento correto para TextArea */
.ant-input-textarea,
.ant-input-textarea textarea,
textarea.ant-input,
.ant-form-item .ant-input-textarea,
.ant-form-item .ant-input-textarea textarea {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  line-height: 1.5715 !important;
}

/* Garantir que rows seja respeitado */
.ant-input-textarea[data-count] {
  height: auto !important;
}

/* Força específica para TextAreas com rows */
textarea[rows="1"] { min-height: calc(1.5715em + 8px) !important; }
textarea[rows="2"] { min-height: calc(3.143em + 8px) !important; }
textarea[rows="3"] { min-height: calc(4.7145em + 8px) !important; }
textarea[rows="4"] { min-height: calc(6.286em + 8px) !important; }
textarea[rows="5"] { min-height: calc(7.8575em + 8px) !important; }
textarea[rows="6"] { min-height: calc(9.429em + 8px) !important; }
