import {
  Button,
  Modal,
  Form,
  Input,
  message,
  Tooltip,
  Typography,
  Select,
} from "antd";
import React, { useMemo, useState } from "react";
import { format } from "date-fns";
import { EditOutlined } from "@ant-design/icons";
import { dynamoGetById, dynamoPost } from "../../../service/apiDsmDynamo";
import { MaskedInput } from "antd-mask-input";
import {
  checksIfEmailAlreadyExists,
  formatOtrsUpdateBody,
  formatUpdateBody,
  otrsUpdate,
  updateDynamo,
} from "../Clients/controllers/clientUsersModal";
import { uniqByKeepLast } from "../../../utils/uniqueByKeepLast";

export const ContactEditModal = (props) => {
  const { Text } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState();
  const [triggerHelper, setTriggerHelper] = useState(false);
  const { getContacts, updateContactOptimistic, client, contact, clients } = props;
  const contactTypes = [
    "Técnico",
    "Financeiro/Administrativo",
    "Gerente",
    "Diretor",
    "Sponsor",
    "Outro",
  ];

  const fieldEdits = [];

  const handleSubmit = async (data) => {
    let nonMaskedPhone = data.phone.replace(/[^A-Z0-9]+/gi, "");
    if (nonMaskedPhone.length < 10) return setTriggerHelper(true);
    setTriggerHelper(false);
    setLoading(true);
    data["identifications"] = {
      itsm_id: data.itsm_id,
      crm_id: data.crm_id,
    };
    data["names"] = {
      first_name: data.first_name,
      last_name: data.last_name,
    };

    delete data.itsm_id;
    delete data.crm_id;
    delete data.first_name;
    delete data.last_name;
    let arr = [];

    const customer =
      (await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-customers`,
        client.id
      )) || client;

    const otrsUpdateBody = formatOtrsUpdateBody(customer, data);
    const updateCustomer = formatUpdateBody(customer, data);
    const foundEmail = checksIfEmailAlreadyExists(clients, data, client.id);

    try {
      if (foundEmail) {
        message.warning("Esse e-mail já pertence a outro contato.");
        return setLoading(false);
      }

      await otrsUpdate(data.identifications.itsm_id, otrsUpdateBody);
      try {
        await updateDynamo(client.id, updateCustomer, data);
        const newArr = uniqByKeepLast(fieldEdits, (e) => e.split(" ")[0]);

        newArr.forEach((e) => {
          return arr.push(e[1]);
        });

        const username = localStorage.getItem("@dsm/username");

        const title = "Contato editado";

        const description = `${username} editou o contato ${
          contact?.names?.first_name
        } ${contact?.names?.last_name}, do cliente ${
          client?.names?.fantasy_name || client?.names?.name
        }, com as seguintes alterações: ${arr}.`;

        dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
          username: username,
          name: title,
          description: description,
          created_at: new Date(),
          updated_at: new Date(),
        });

        if (updateContactOptimistic && typeof updateContactOptimistic === 'function') {
          const updatedContactData = {
            ...contact,
            names: {
              first_name: data.names.first_name,
              last_name: data.names.last_name
            },
            email: data.email,
            phone: data.phone,
            position: data.position,
            contact_type: data.contact_type,
            authorized_contact: data.authorized_contact,
            updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss")
          };

          updateContactOptimistic(contact.identifications.itsm_id, updatedContactData);
        }

        setShowModal(false);
        message.success("Contato editado com sucesso!");

        // Aguardar propagação do DynamoDB (eventual consistency) - tempo reduzido
        await new Promise(resolve => setTimeout(resolve, 300));

        // Atualização única com forceRefresh (suficiente após atualização otimística)
        try {
          await getContacts(true); // forceRefresh = true
        } catch (error) {
          console.warn('⚠️ ContactEdit: Erro na sincronização:', error);
        }

        setLoading(false);
      } catch (err) {
        console.log(err);
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  };
  const setFormValues = () => {
    form.setFieldsValue({
      itsm_id: contact?.identifications?.itsm_id,
      crm_id: contact?.identifications?.crm_id,
      first_name: contact?.names?.first_name,
      last_name: contact?.names?.last_name,
      customer_id: contact?.customer_id,
      active: contact?.active,
      email: contact?.email,
      phone: contact?.phone,
      contact_type: contact?.contact_type,
      authorized_contact: contact?.authorized_contact,
    });
  };

  const obj = {
    identifications: {
      itsm_id: contact?.identifications?.itsm_id,
      crm_id: contact?.identifications?.crm_id,
    },
    names: {
      first_name: contact?.names?.first_name,
      last_name: contact?.names?.last_name,
    },
    customer_id: contact?.customer_id,
    active: contact?.active,
    email: contact?.email,
    phone: contact?.phone,
  };

  function getChange(key) {
    for (const [objectKey, objectValue] of Object.entries(obj)) {
      if (objectKey === key) {
        return objectValue;
      }
      if (typeof objectValue === "object" && objectValue !== null) {
        for (const [subObjectKey, subObjectValue] of Object.entries(
          objectValue
        )) {
          if (subObjectKey === key) {
            return subObjectValue;
          }
        }
      }
    }
    return "";
  }

  const mask = useMemo(
    () => [
      {
        mask: "(00) 0 0000-0000",
        lazy: false,
      },
      {
        mask: "(00) 0000-0000",
        lazy: false,
      },
    ],
    []
  );

  const handleChange = (data) => {
    const fieldsName = {
      itsm_id: "ITSM",
      crm_id: "CRM",
      first_name: "Primeiro Nome",
      last_name: "Último Nome",
      email: "Email",
      phone: "Telefone",
      contact_type: "Tipo do contato",
      authorized_contact: "Contato autorizado",
    };
    fieldEdits.push(
      `${fieldsName[Object.keys(data)[0]]} alterado de: ${
        getChange(Object.keys(data)[0]) || '" - "'
      } para ${Object.values(data)[0]}`
    );
  };

  return (
    <>
      <Tooltip title="Editar informações do contato">
        <Button
          type="text"
          onClick={async () => {
            setShowModal(true);
            setFormValues();
          }}
        >
          <EditOutlined />
        </Button>
      </Tooltip>
      <Modal
        title="Editar Contato"
        open={showModal}
        closable={false}
        onOk={() => form.submit()}
        okText="Editar"
        confirmLoading={loading}
        cancelText="Cancelar"
        onCancel={() => {
          setShowModal(false);
        }}
      >
        <Form
          onValuesChange={handleChange}
          form={form}
          requiredMark={false}
          onFinish={handleSubmit}
          layout="vertical"
        >
          <Form.Item hidden name="id" />
          <Form.Item name="customer_id" hidden />
          <Form.Item hidden name="active" />
          <Form.Item name="crm_id" label="CRM ID">
            <Input placeholder="CRM ID" />
          </Form.Item>
          <Form.Item name="itsm_id" label="ITSM ID">
            <Input placeholder="ITSM ID" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="first_name"
            label="Primeiro Nome"
          >
            <Input placeholder="Primeiro Nome" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="last_name"
            label="Último Nome"
          >
            <Input placeholder="Último Nome" />
          </Form.Item>
          <Form.Item
            rules={[
              { required: true, message: "E-mail inválido.", type: "email" },
            ]}
            name="email"
            label="Email"
          >
            <Input placeholder="Email" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            name="phone"
            label="Telefone"
            help={
              triggerHelper === true && (
                <Text type="danger">Telefone inválido</Text>
              )
            }
          >
            <MaskedInput
              style={{ border: triggerHelper === true && "solid 1px #ff0000" }}
              placeholder="Telefone"
              mask={mask}
              maskOptions={{
                dispatch: function (appended, dynamicMasked) {
                  const isCellPhone = dynamicMasked.unmaskedValue[2] === "9";
                  return dynamicMasked.compiledMasks[isCellPhone ? 0 : 1];
                },
              }}
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Selecione uma opção." }]}
            name="authorized_contact"
            label="Contato Autorizado"
          >
            <Select placeholder="Contato Autorizado">
              <Option value="Sim">Sim</Option>
              <Option value="Não">Não</Option>
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Selecione uma opção." }]}
            name="contact_type"
            label="Tipo de Contato"
          >
            <Select placeholder="Tipo de Contato">
              {contactTypes.map((type) => (
                <Option value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
