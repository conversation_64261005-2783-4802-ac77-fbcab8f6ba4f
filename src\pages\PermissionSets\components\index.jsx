import React, { useEffect, useMemo, useState } from "react";
import {
  Row,
  Col,
  Input,
  Button,
  Popconfirm,
  Checkbox,
  Tooltip,
  Space,
  Select,
  Typography,
  Modal,
  Form,
  Divider,
  message,
} from "antd";
import {
  DeleteOutlined,
  TeamOutlined,
  PlusOutlined,
  MinusCircleOutlined,
  LikeOutlined,
  EditOutlined,
} from "@ant-design/icons";
import {
  deleteMultiplePermissionSets,
  deletePermissionSet,
  linkPermissionSet,
  submitPermissionSetData,
} from "../controllers";
import { DynamicTable } from "../../../components/Table/DynamicTable";
import { filterTableData } from "../../../utils/filterTableData";
import { shallowEqual, useSelector } from "react-redux";

export const PermissionSetsHeader = (props) => {
  const { Text } = Typography;
  const { Option } = Select;
  const {
    setSearch,
    multiplePermissionSets,
    setMultiplePermissionSets,
    selectMultiplePermissionSets,
    setSelectMultiplePermissionSets,
    setLoading,
    setSolicitations,
    setState,
  } = props;

  console.log("PermissionSetsHeader props", multiplePermissionSets);
  return (
    <Row justify="space-between">
      <Col span={6} style={{ marginBottom: "1em", borderRadius: "15px" }}>
        <Input
          placeholder="Busque um usuário"
          style={{
            height: "35px",
            borderRadius: "7px",
          }}
          onChange={(e) => setSearch(e.target.value)}
        />
      </Col>
      <PermissionSetActions
        setSolicitations={setSolicitations}
        setLoading={setLoading}
      />
      <SelectMutipleButton
        selectMultiplePermissionSets={selectMultiplePermissionSets}
        setSelectMultiplePermissionSets={setSelectMultiplePermissionSets}
        multiplePermissionSets={multiplePermissionSets}
      />
      <DeleteMultipleButtonHandler
        multiplePermissionSets={multiplePermissionSets}
        selectMultiplePermissionSets={selectMultiplePermissionSets}
        setLoading={setLoading}
        setSolicitations={setSolicitations}
        setMultiplePermissionSets={setMultiplePermissionSets}
      />
      <Col>
        <Space>
          <Text>Filtrar por: </Text>
          <Select onChange={setState} defaultValue="todos">
            <Option value="todos">Todos</Option>
            <Option value="active">Ativos</Option>
            <Option value="inativos">Inativos</Option>
          </Select>
        </Space>
      </Col>
    </Row>
  );
};

const SelectMutipleButton = ({
  selectMultiplePermissionSets,
  setSelectMultiplePermissionSets,
  multiplePermissionSets,
}) => {
  return (
    <Col
      span={
        multiplePermissionSets.length > 1 &&
        selectMultiplePermissionSets === true
          ? 6
          : 12
      }
    >
      <Button
        type="default"
        onClick={() => {
          setSelectMultiplePermissionSets(!selectMultiplePermissionSets);
        }}
      >
        Selecionar{" "}
        {selectMultiplePermissionSets === false
          ? "vários Permission Sets"
          : "um Permission Set"}{" "}
      </Button>
    </Col>
  );
};

const DeleteMultipleButtonHandler = (props) => {
  const {
    multiplePermissionSets,
    selectMultiplePermissionSets,
    setLoading,
    setSolicitations,
    setMultiplePermissionSets,
  } = props;
  return (
    <>
      {multiplePermissionSets.length > 1 &&
      selectMultiplePermissionSets === true ? (
        <Col span={6}>
          <ConfirmWarning
            setLoading={setLoading}
            setSolicitations={setSolicitations}
            multiplePermissionSets={multiplePermissionSets}
            selectMultiplePermissionSets={selectMultiplePermissionSets}
            setMultiplePermissionSets={setMultiplePermissionSets}
          />
        </Col>
      ) : null}
    </>
  );
};

export const DeletePermissionSetCell = (props) => {
  const {
    item,
    multiplePermissionSets,
    selectMultiplePermissionSets,
    setMultiplePermissionSets,
    setLoading,
    setSolicitations,
  } = props;
  return selectMultiplePermissionSets === false ? (
    <Tooltip title="Confirme a deleção do Permission Set" placement="left">
      <ConfirmWarning
        setLoading={setLoading}
        setSolicitations={setSolicitations}
        item={item}
        multiplePermissionSets={multiplePermissionSets}
        selectMultiplePermissionSets={selectMultiplePermissionSets}
      />
    </Tooltip>
  ) : (
    <Row align="middle" justify="center">
      <Col>
        <Checkbox
          checked={multiplePermissionSets.includes(item.id)}
          value={item.id}
          onChange={(e) => {
            if (multiplePermissionSets.includes(e.target.value)) {
              setMultiplePermissionSets(
                multiplePermissionSets.filter((x) => x !== e.target.value)
              );
            } else {
              setMultiplePermissionSets([
                ...multiplePermissionSets,
                e.target.value,
              ]);
            }
          }}
        ></Checkbox>
      </Col>
    </Row>
  );
};

const ConfirmWarning = (props) => {
  const {
    setLoading,
    setSolicitations,
    item,
    multiplePermissionSets,
    selectMultiplePermissionSets,
    setMultiplePermissionSets,
  } = props;

  const safeSetMultiplePermissionSets = setMultiplePermissionSets || (() => {});
  return (
    <Tooltip title="Confirme a deleção do Permission Set" placement="left">
      <Popconfirm
        title={`Tem certeza de que você quer deletar ${
          selectMultiplePermissionSets
            ? `${multiplePermissionSets.length} Permission Sets`
            : "este Permission Set"
        }?`}
        onConfirm={async () => {
          setLoading(true);
          if (selectMultiplePermissionSets)
            await deleteMultiplePermissionSets(
              multiplePermissionSets,
              setSolicitations
            );
          else await deletePermissionSet(setSolicitations, item);
          safeSetMultiplePermissionSets([]);
          setLoading(false);
        }}
        placement="leftTop"
        trigger="click"
        cancelText="Cancelar"
      >
        <Button danger type="text">
          <DeleteOutlined />
        </Button>
      </Popconfirm>
    </Tooltip>
  );
};

export const ViewUsersLinkedToPermissionSet = (props) => {
  const { permissionSetData } = props;
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const columns = [
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      align: "center",
    },
    {
      title: "Cargo",
      dataIndex: "role",
      key: "role",
      align: "center",
    },
  ];

  const pagination = {
    pageSize: 5,
    hideOnSinglePage: true,
  };

  const tableData = useMemo(() => {
    let data = permissionSetData.users || [];
    if (search) {
      const searchFields = ["email", "role"];
      data = filterTableData({ searchFields, search, data });
    }
    return data;
  }, [permissionSetData.users, search]);

  return (
    <>
      <Button
        icon={<TeamOutlined />}
        onClick={() => setOpen(true)}
        type="link"
        disabled={!permissionSetData.users}
      />
      <Modal
        title="Usuários vinculados ao Permission Set"
        open={open}
        onCancel={() => setOpen(false)}
        onOk={() => setOpen(false)}
        cancelText="Cancelar"
      >
        <Input
          placeholder="Busque um usuário"
          onChange={(e) => setSearch(e.target.value)}
          style={{ marginBottom: "1em" }}
        />

        <DynamicTable
          columns={columns}
          data={tableData}
          pagination={pagination}
        />
      </Modal>
    </>
  );
};

const PermissionSetActions = (props) => {
  const permissionSets = useSelector(
    (state) => state.switchRole.permissionSets,
    shallowEqual
  );
  const { setSolicitations, setLoading, type, item = "create" } = props;
  const { Text } = Typography;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [formLoading, setFormLoading] = useState(false);

  useEffect(() => {
    form.resetFields();
  }, [open]);

  const regexDaredeEmailRule = /^[a-zA-Z]+\.[a-zA-Z]+@darede\.com\.br$/;

  return (
    <Col key={item.id}>
      <Button
        type={type === "edit" ? "link" : "primary"}
        onClick={() => {
          setOpen(true);
        }}
        disabled={!item?.users && type === "edit" ? true : false}
      >
        {type === "edit" ? <EditOutlined /> : "Cadastrar Permission Set"}
      </Button>
      <Modal
        title="Cadastrar Permission Set"
        open={open}
        onCancel={() => {
          setOpen(false);
        }}
        footer={[
          <Button key="back" onClick={() => setOpen(false)}>
            Cancelar
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={formLoading}
            onClick={() => {
              const formValues = form.getFieldsValue();
              if (formValues?.users?.length < 1 || !formValues.users) {
                message.error(
                  "Nenhum usuário foi adicionado ao Permission Set."
                );
                return;
              } else {
                form.submit();
              }
            }}
          >
            Salvar
          </Button>,
        ]}
      >
        <Form
          form={form}
          onFinish={() => {
            submitPermissionSetData(
              form,
              setSolicitations,
              type,
              setLoading,
              item,
              permissionSets,
              setOpen,
              setFormLoading
            );
          }}
          layout="vertical"
        >
          <Form.Item
            name="name"
            rules={[{ required: true, message: "Nome é obrigatório" }]}
            label="Nome do Permission Set"
            initialValue={item?.name}
          >
            <Input placeholder="Ex.: academy-permission" />
          </Form.Item>
          <Form.Item
            name="arn"
            rules={[{ required: true, message: "ARN é obrigatório" }]}
            label="ARN"
            initialValue={item?.arn}
          >
            <Input placeholder="Ex.:arn:aws:sso:::permissionSet/ssoins-xxxxxxxxxxxxxxxx/ps-xxxxxxxxxxxxxxxx" />
          </Form.Item>
          <Divider />
          <Space style={{ marginBottom: "1em" }}>
            <Text>Usuários vinculados:</Text>
          </Space>
          <Form.List
            initialValue={item?.users || []}
            name="users"
            rules={[
              {
                validator: async (_, names) => {
                  if (!names || names.length < 1) {
                    return Promise.reject(new Error("Adicione um usuário"));
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }) => (
              <>
                {fields.map((field) => (
                  <Space
                    key={field.key}
                    style={{ display: "flex", marginBottom: 8 }}
                    align="baseline"
                  >
                    <Form.Item
                      {...field}
                      name={[field.name, "email"]}
                      key={[field.key, "email"]}
                      rules={[
                        {
                          required: true,
                          message: `Email é obrigatório e precisa ser válido (<EMAIL>)`,
                          pattern: regexDaredeEmailRule,
                        },
                      ]}
                    >
                      <Input placeholder="Email" />
                    </Form.Item>
                    <Form.Item
                      {...field}
                      name={[field.name, "role"]}
                      key={[field.key, "role"]}
                      rules={[
                        { required: true, message: "Cargo é obrigatório" },
                      ]}
                    >
                      <Input placeholder="Cargo" />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(field.name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    Adicionar usuário vinculado ao Permission Set
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </Col>
  );
};

export const ActionsCell = (props) => {
  const { item, setSolicitations, setLoading } = props;
  return (
    <Space>
      <LinkPermissionSet
        item={item}
        setSolicitations={setSolicitations}
        setLoading={setLoading}
      />
      <PermissionSetActions
        type="edit"
        item={item}
        setSolicitations={setSolicitations}
        setLoading={setLoading}
      />
    </Space>
  );
};

const LinkPermissionSet = (props) => {
  const { item, setSolicitations, setLoading } = props;
  return (
    <Tooltip
      title="Confirme a atribuição de acesso do usuário"
      placement="left"
    >
      <Popconfirm
        title="Tem certeza de que já atribuiu a role necessária para este usuário?"
        onConfirm={async () => {
          await linkPermissionSet(setSolicitations, item, setLoading);
        }}
        placement="leftTop"
        trigger="click"
        cancelText="Cancelar"
      >
        <Button type="text">
          <LikeOutlined />
        </Button>
      </Popconfirm>
    </Tooltip>
  );
};
