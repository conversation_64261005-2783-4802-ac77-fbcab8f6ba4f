/**
 * Utilitário para verificação de saúde da API OTRS
 * Monitora conectividade e performance das chamadas OTRS
 */

import { otrsGet } from '../service/apiOtrs';
import { logger } from './logger';

class OtrsHealthMonitor {
  constructor() {
    this.lastHealthCheck = null;
    this.isHealthy = null;
    this.consecutiveFailures = 0;
    this.maxConsecutiveFailures = 3;
    this.healthCheckInterval = 5 * 60 * 1000; // 5 minutos
  }

  /**
   * Verifica se a API OTRS está saudável
   */
  async checkHealth() {
    const now = Date.now();
    
    // Se já verificou recentemente e está saudável, retornar cache
    if (this.lastHealthCheck && 
        (now - this.lastHealthCheck) < this.healthCheckInterval && 
        this.isHealthy) {
      return this.isHealthy;
    }

    try {
      console.log('🏥 Verificando saúde da API OTRS...');
      
      const startTime = Date.now();
      
      // Fazer uma chamada simples para testar conectividade
      // Usando endpoint que deve sempre existir
      await otrsGet('health'); // ou outro endpoint de health check
      
      const responseTime = Date.now() - startTime;
      
      this.isHealthy = true;
      this.consecutiveFailures = 0;
      this.lastHealthCheck = now;
      
      console.log('✅ API OTRS está saudável', {
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString()
      });
      
      return true;
      
    } catch (error) {
      this.consecutiveFailures++;
      this.isHealthy = false;
      this.lastHealthCheck = now;
      
      console.error('❌ API OTRS não está saudável:', {
        error: error.message,
        status: error.response?.status,
        consecutiveFailures: this.consecutiveFailures,
        timestamp: new Date().toISOString()
      });
      
      // Se muitas falhas consecutivas, alertar
      if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        console.error('🚨 ALERTA: API OTRS com múltiplas falhas consecutivas!', {
          failures: this.consecutiveFailures,
          maxAllowed: this.maxConsecutiveFailures
        });
      }
      
      return false;
    }
  }

  /**
   * Verifica se deve prosseguir com operação OTRS
   */
  async shouldProceedWithOtrsOperation() {
    const isHealthy = await this.checkHealth();
    
    if (!isHealthy) {
      console.warn('⚠️ API OTRS não está saudável, operação pode falhar');
      return {
        proceed: true, // Ainda tenta, mas com aviso
        warning: 'API OTRS pode estar instável. A operação será tentada, mas pode falhar.'
      };
    }
    
    return { proceed: true };
  }

  /**
   * Obtém estatísticas de saúde
   */
  getHealthStats() {
    return {
      isHealthy: this.isHealthy,
      lastCheck: this.lastHealthCheck ? new Date(this.lastHealthCheck).toISOString() : null,
      consecutiveFailures: this.consecutiveFailures,
      maxFailuresAllowed: this.maxConsecutiveFailures
    };
  }

  /**
   * Reset das estatísticas
   */
  reset() {
    this.lastHealthCheck = null;
    this.isHealthy = null;
    this.consecutiveFailures = 0;
    console.log('🔄 Estatísticas de saúde OTRS resetadas');
  }
}

// Instância singleton
const otrsHealthMonitor = new OtrsHealthMonitor();

/**
 * Função utilitária para verificar saúde antes de operações críticas
 */
export const checkOtrsHealth = async () => {
  return await otrsHealthMonitor.checkHealth();
};

/**
 * Função para verificar se deve prosseguir com operação OTRS
 */
export const shouldProceedWithOtrsOperation = async () => {
  return await otrsHealthMonitor.shouldProceedWithOtrsOperation();
};

/**
 * Função para obter estatísticas de saúde
 */
export const getOtrsHealthStats = () => {
  return otrsHealthMonitor.getHealthStats();
};

/**
 * Função para resetar estatísticas
 */
export const resetOtrsHealth = () => {
  otrsHealthMonitor.reset();
};

/**
 * Middleware para adicionar verificação de saúde em operações críticas
 */
export const withOtrsHealthCheck = async (operation, operationName = 'OTRS Operation') => {
  console.log(`🏥 Verificando saúde antes de: ${operationName}`);
  
  const healthCheck = await shouldProceedWithOtrsOperation();
  
  if (healthCheck.warning) {
    console.warn(`⚠️ ${operationName}: ${healthCheck.warning}`);
  }
  
  if (!healthCheck.proceed) {
    throw new Error(`Operação ${operationName} cancelada devido a problemas de saúde da API OTRS`);
  }
  
  try {
    const result = await operation();
    console.log(`✅ ${operationName} concluída com sucesso`);
    return result;
  } catch (error) {
    console.error(`❌ ${operationName} falhou:`, error.message);
    throw error;
  }
};

export default otrsHealthMonitor;
