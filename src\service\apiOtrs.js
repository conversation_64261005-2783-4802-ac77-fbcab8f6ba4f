import Axios from "axios";
import { dsmProvider } from "../provider/dsm-provider";
import { authService } from "../services/authService";

export const generateToken = async () => {
  try {
    const provider = dsmProvider();
    let jwt = localStorage.getItem("jwt");

    const { data } = await provider.get("/cognito/access-token", {
      headers: {
        Authorization: jwt,
      },
    });
    return data.data;
  } catch (error) {
    console.log(error);

    // Fallback: tenta com configuração manual
    try {
      const token = authService.getToken();
      if (!token) {
        throw new Error('Token de autenticação não encontrado');
      }

      const { data } = await Axios.get(
        `${process.env.REACT_APP_API_PERMISSION}/cognito/access-token`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return data.data;
    } catch (fallbackError) {
      console.error('Erro no fallback de geração de token:', fallbackError);
      throw fallbackError;
    }
  }
};

export const otrsGet = async (route, params) => {
  const token = await generateToken();

  const { data } = await Axios.get(
    process.env.REACT_APP_API_OTRS_BASE_URL + route,
    {
      headers: { Authorization: token },
      params,
    }
  );

  return data;
};

export const otrsPost = async (route, body, retryCount = 0) => {
  const maxRetries = 3;
  const retryDelay = 1000 * (retryCount + 1); // 1s, 2s, 3s

  try {
    const token = await generateToken();

    if (!token) {
      throw new Error('Falha na geração do token de autenticação OTRS');
    }

    console.log('🎯 otrsPost Debug:', {
      route,
      baseUrl: process.env.REACT_APP_API_OTRS_BASE_URL,
      fullUrl: process.env.REACT_APP_API_OTRS_BASE_URL + route,
      hasToken: !!token,
      retryCount,
      timestamp: new Date().toISOString()
    });

    // Validar URL base
    if (!process.env.REACT_APP_API_OTRS_BASE_URL) {
      throw new Error('URL base do OTRS não configurada (REACT_APP_API_OTRS_BASE_URL)');
    }

    const config = {
      timeout: 30000, // 30 segundos
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    if (route.includes("create/ticket")) {
      config.headers.Authorization = token;
      config.headers.auth = process.env.REACT_APP_OTRS_HOURS_AUTH;
    } else {
      config.headers.Authorization = route === "read/hours/contract/new/rule"
        ? process.env.REACT_APP_OTRS_HOURS_AUTH
        : token;
    }

    console.log('🔄 Enviando requisição OTRS:', {
      method: 'POST',
      url: process.env.REACT_APP_API_OTRS_BASE_URL + route,
      hasBody: !!body,
      bodyKeys: body ? Object.keys(body) : [],
      timeout: config.timeout
    });

    const { data } = await Axios.post(
      process.env.REACT_APP_API_OTRS_BASE_URL + route,
      { ...body },
      config
    );

    console.log('✅ Resposta OTRS recebida:', {
      route,
      hasData: !!data,
      dataKeys: data ? Object.keys(data) : [],
      retryCount
    });

    return data;

  } catch (error) {
    console.error('❌ Erro na requisição OTRS:', {
      route,
      retryCount,
      maxRetries,
      error: {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      }
    });

    // Retry logic para erros temporários
    if (retryCount < maxRetries && shouldRetry(error)) {
      console.log(`🔄 Tentativa ${retryCount + 1}/${maxRetries} em ${retryDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      return otrsPost(route, body, retryCount + 1);
    }

    // Melhorar mensagem de erro
    const errorMessage = getOtrsErrorMessage(error);
    throw new Error(errorMessage);
  }
};

// Função para determinar se deve fazer retry
const shouldRetry = (error) => {
  const status = error.response?.status;
  // Retry para erros de servidor (5xx) e timeout
  return !status || status >= 500 || error.code === 'ECONNABORTED';
};

// Função para obter mensagem de erro amigável
const getOtrsErrorMessage = (error) => {
  const status = error.response?.status;
  const data = error.response?.data;

  if (status === 401) {
    return 'Token de autenticação inválido ou expirado. Faça login novamente.';
  } else if (status === 403) {
    return 'Acesso negado. Verifique suas permissões.';
  } else if (status === 404) {
    return 'Endpoint OTRS não encontrado. Verifique a configuração.';
  } else if (status >= 500) {
    return `Erro interno do servidor OTRS (${status}). Tente novamente.`;
  } else if (error.code === 'ECONNABORTED') {
    return 'Timeout na conexão com OTRS. Verifique sua conexão.';
  } else if (data?.message) {
    return `Erro OTRS: ${data.message}`;
  } else {
    return `Erro na comunicação com OTRS: ${error.message}`;
  }
};

export const createTicket = async (body) => {
  const token = await generateToken();

  const { data } = await Axios.post(
    process.env.REACT_APP_API_OTRS_BASE_URL + "create/ticket",
    body,
    {
      headers: {
        Authorization: token,
        auth: process.env.REACT_APP_OTRS_HOURS_AUTH,
      },
    }
  );

  return data;
};

export const otrsPut = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.put(
    process.env.REACT_APP_API_OTRS_BASE_URL + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const ticketPost = async (route, body) => {
  const token = await generateToken();

  if (route.includes("create/ticket")) {
    const { data } = await Axios.post(
      process.env.REACT_APP_TICKET_API + route,
      { ...body },
      {
        headers: {
          Authorization: token,
          auth: process.env.REACT_APP_OTRS_HOURS_AUTH,
        },
      }
    );

    return data;
  } else {
    const { data } = await Axios.post(
      process.env.REACT_APP_TICKET_API + route,
      { ...body },
      {
        headers: { Authorization: token },
      }
    );

    return data;
  }
};

export const ticketPut = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.put(
    process.env.REACT_APP_TICKET_API + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const otrsDelete = async (route, body) => {
  const token = await generateToken();

  const { data } = await Axios.delete(
    process.env.REACT_APP_API_BASE_OTRS_URL + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};
