import { DatePicker } from "antd";
import moment from "moment";
import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

import { setUploadFilesState } from "../../../store/actions/files-action";

export const Date = () => {
  const handleChange = (e) => {
    if (e !== null) {
      // Converter para string serializável antes de salvar no Redux
      setUploadFilesState({
        field: "dateRange",
        value: e.format('YYYY-MM-DD'),
      });
    } else {
      setUploadFilesState({
        field: "dateRange",
        value: "",
      });
    }
  };

  return (
    <>
      <DatePicker
        placeholder="Selecione a Data"
        picker="month"
        locale={locale}
        format="MMMM/YYYY"
        disabledDate={(current) => {
          // Limitar seleção entre 2 anos atrás e o mês atual
          const twoYearsAgo = moment().subtract(2, 'years').startOf('month');
          const currentMonth = moment().endOf('month');
          return current && (current < twoYearsAgo || current > currentMonth);
        }}
        onChange={(e) => handleChange(e)}
      />
    </>
  );
};
