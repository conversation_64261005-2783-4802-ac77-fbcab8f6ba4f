import { createSlice } from "@reduxjs/toolkit";
import { getMonth, getYear } from "date-fns";
import dayjs from "dayjs";

const REDUCER_NAME = "invoices";

export const COLLUMNS_INVOICES = [
  "Dolar",
  "Bill Payer Account",
  "Account ID",
  "Customer",
  "Billing Entity",
  "Invoice ID",
  "Month",
  "Year",
  "Charges (US$)",
  "EDP Discount",
  "SPP Discount",
  "Credits",
  "Bundled Discount",
  "Saving Plans Discount",
  "Outros Descontos",
  "Total Discount",
  "Tax (US$)",
  "Tax (%)",
  "Line Item Legal Entity",
  "Total (US$)",
  "Total (R$)",
  "Final Balance (US$)",
  "Final Balance (R$)",
];

export const INVOICE_VIEW_STATES = {
  SUMMARY: "summary",
  DETAILD: "detaild",
};

const INITIAL_STATE = {
  search: "",
  invoices: [],
  permissions: [],
  dolar: 0,
  totalCash: {
    total_neg: 0,
    total_pos: 0,
    total_refund: 0,
    total_edp: 0,
    total_spp: 0,
  },
  dateRange: {
    month: getMonth(new Date()) + 1,
    year: getYear(new Date()),
  },
  view: INVOICE_VIEW_STATES.SUMMARY,
  loading: false,
  collumnsToShow: COLLUMNS_INVOICES,
  collumnsInvoices: COLLUMNS_INVOICES,
};

const invoiceSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setInvoiceFieldReduce(state, action) {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }
      state[action.payload.field] = value;
    },
    setInvoicesDateRangeReduce(state, action) {
      state.dateRange = action.payload;
    },
    cleanInvoiceReduce(state, action) {
      state.search = "";
      state.view = INVOICE_VIEW_STATES.SUMMARY;
      state.loading = false;
    },
  },
});

export const {
  setInvoiceFieldReduce,
  setInvoicesDateRangeReduce,
  cleanInvoiceReduce,
} = invoiceSlice.actions;

export default invoiceSlice.reducer;
