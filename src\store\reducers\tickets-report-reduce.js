import { createSlice, createSelector } from "@reduxjs/toolkit";
import moment from "moment";
import dayjs from "dayjs";
import { serializeDate, deserializeDate } from "../../utils/dateSerializer";

const REDUCER_NAME = "ticketsReport";

export const allOption = { value: "Todos", label: "Todos" };

export const INITIAL_STATE = {
  customers: [],
  customerSelected: null,
  contracts: [allOption],
  contractSelected: allOption,
  tickets: [],
  search: "",
  filteredTickets: [],
  month: serializeDate(moment()), // Serialize moment object
};

const ticketReportSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setTicketReportReduce(state, action) {
      const { field, value } = action.payload;

      // Handle moment/dayjs objects by serializing them
      if (field === 'month' && (moment.isMoment(value) || (value && value.isDayjsObject))) {
        state[field] = serializeDate(value);
      } else if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        // Converter outros objetos dayjs/moment para strings
        state[field] = value.format('YYYY-MM-DD');
      } else {
        state[field] = value;
      }
    },
  },
});

export const { setTicketReportReduce } = ticketReportSlice.actions;

// Selectors to handle serialized dates with proper memoization
const selectTicketReportMonthValue = (state) => state.ticketReport.month;

export const selectTicketReportMonth = createSelector(
  [selectTicketReportMonthValue],
  (monthValue) => {
    // Memoized selector that only recalculates when monthValue changes
    return typeof monthValue === 'string' ? deserializeDate(monthValue) : moment();
  }
);

export default ticketReportSlice.reducer;
