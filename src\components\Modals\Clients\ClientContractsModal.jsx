import { Col, Row, Tag, Input, Modal, Table, But<PERSON>, Tooltip } from "antd";
import { useState } from "react";
import { SnippetsOutlined } from "@ant-design/icons";
import { ExecutivesModal } from "../Contracts/ExecutivesModal";
import { EditTableContractModal } from "../Contracts/EditTableContractModal";
import { VisualizeInfo } from "../Contracts/VisualizeInfo";
import { getCustomerContracts } from "../../../controllers/clients/clientsController";
import { InactiveContractReason } from "../Contracts/InactiveContractReason";
import * as controller from "../../../controllers/contracts/contract-controller";

export const ClientContractsModal = (props) => {
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState();
  const { client, permissions, tables, executivesData, contractsPermissions } =
    props;
  const [data, setData] = useState([]);
  const [search, setSearch] = useState("");

  const getCurrentCustomerContracts = async () => {
    setLoading(true);
    try {
      console.log('ClientContractsModal: Buscando contratos para cliente:', {
        clientId: client?.id,
        clientName: client?.names?.name || client?.names?.fantasy_name,
        client: client
      });

      const currentCustomerContracts = await getCustomerContracts(client.id);

      // Debug: verificar estrutura dos dados
      if (process.env.NODE_ENV === 'development') {
        console.log('ClientContractsModal: Dados recebidos:', {
          clientId: client.id,
          hasData: !!currentCustomerContracts,
          dataType: typeof currentCustomerContracts,
          isArray: Array.isArray(currentCustomerContracts),
          length: currentCustomerContracts?.length,
          firstItem: currentCustomerContracts?.[0] ? Object.keys(currentCustomerContracts[0]) : 'N/A',
          rawData: currentCustomerContracts
        });
      }

      setData(currentCustomerContracts || []);
    } catch (error) {
      console.error('Erro ao buscar contratos do cliente:', error);
      setData([]);
    }
    setLoading(false);
  };

  const handleOpenModal = async () => {
    setShowModal(true);
    await getCurrentCustomerContracts();
  };

  const columns = [
    {
      code: "view_contract_dsm_id",
      title: "DSM ID",
      align: "center",
      dataIndex: "dsm_id",
      sorter: (a, b) => a.dsm_id?.localeCompare(b.dsm_id),
      render: (field, item) => item.dsm_id || "-",
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_contract_crm",
      title: "CRM",
      dataIndex: ["identifications", "crm_id"],
      sorter: (a, b) => a?.identifications?.crm_id - b?.identifications?.crm_id,
      sortDirections: ["descend", "ascend"],
      width: "1%",
    },
    {
      code: "view_contract_itsm",
      title: "ITSM",
      dataIndex: ["identifications", "itsm_id"],
      defaultSortOrder: "descend",
      sorter: (a, b) =>
        a?.identifications?.itsm_id - b?.identifications?.itsm_id,
      sortDirections: ["descend", "ascend"],
      width: "1%",
    },
    {
      code: "view_contract_name",
      title: "Nome",
      dataIndex: "name",
      key: "name",
    },
    {
      code: "view_contract_active",
      title: "Ativo",
      dataIndex: "active",
      key: "active",
      width: "1%",
      render: (e) => {
        if (e === 1) {
          return <Tag color="green">Ativo</Tag>;
        } else {
          return <Tag color="red">Inativo</Tag>;
        }
      },
    },
    {
      code: "view_contract_edit",
      title: "Editar",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (id, item) => {
        return (
          <EditTableContractModal
            getCustomerContracts={getCurrentCustomerContracts}
            contracts={data}
            contract={item}
            client={client}
            permissions={contractsPermissions?.data || permissions?.data}
            tables={tables}
          />
        );
      },
    },
    {
      code: "view_contract_executives",
      title: "Executivos",
      dataIndex: "executives",
      key: "executives",
      width: "1%",
      render: (field, item) => {
        return (
          <Row justify="center">
            <Col>
              <ExecutivesModal
                getCustomerContracts={getCurrentCustomerContracts}
                permissions={
                  Array.isArray(contractsPermissions?.data) ? contractsPermissions.data :
                  Array.isArray(contractsPermissions) ? contractsPermissions :
                  Array.isArray(permissions?.data) ? permissions.data :
                  Array.isArray(permissions) ? permissions : []
                }
                executivesData={executivesData}
                contracts={data}
                executives={field}
                contract={item}
              />
            </Col>
          </Row>
        );
      },
    },
    {
      code: "view_contract_info",
      title: "Informações",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (field, item) => {
        return (
          <Row justify="center">
            <Col>
              <VisualizeInfo
                contract={item}
                userPermissions={
                  Array.isArray(contractsPermissions?.data) ? contractsPermissions.data :
                  Array.isArray(contractsPermissions) ? contractsPermissions :
                  Array.isArray(permissions?.data) ? permissions.data :
                  Array.isArray(permissions) ? permissions : []
                }
              />
            </Col>
          </Row>
        );
      },
    },
    {
      code: "view_contract_actions",
      title: "Ações",
      dataIndex: "active",
      key: "active",
      width: "1%",
      render: (field, item) => {
        return (
          <InactiveContractReason
            contract={item}
            field={field}
            loading={loading}
            setLoading={setLoading}
            controller={controller}
            getCurrentCustomerContracts={getCurrentCustomerContracts}
          />
        );
      },
    },
  ];

  const pagination = {
    data: [],
  };

  return (
    <>
      <Row justify="center">
        <Tooltip title="Visualizar contratos">
          <Button
            type="text"
            style={{
              color: client?.has_active_contracts !== 2 ? "#0f9347" : "#333",
            }}
            onClick={async () => {
              handleOpenModal();
            }}
          >
            <SnippetsOutlined />
          </Button>
        </Tooltip>
      </Row>
      <Modal
        width="60vw"
        onCancel={() => {
          setShowModal(false);
        }}
        title="Contratos"
        open={showModal}
        closable={false}
        footer={[
          <Button
            type="primary"
            onClick={() => {
              setShowModal(false);
            }}
          >
            Fechar
          </Button>,
        ]}
      >
        <Row justify="center">
          <Col>
            <Input
              onChange={(e) => setSearch(e.target.value)}
              style={{
                width: "300px",
                height: "35px",
                borderRadius: "7px",
                marginBottom: "2em",
              }}
              placeholder="Buscar contrato..."
            />
          </Col>
        </Row>
        <Row
          align="center"
          style={{
            flexWrap: "nowrap",
            overflowX: "auto",
            paddingBottom: "10px",
          }}
        >
          <Table
            loading={loading}
            style={{ minWidth: "100%" }}
            dataSource={data?.filter((e) => {
              const data = [
                e?.identifications?.itsm_id
                  ?.toString()
                  ?.toLowerCase()
                  .includes(search.toLowerCase()) || false,
                e?.identifications?.crm_id
                  ?.toString()
                  ?.toLowerCase()
                  .includes(search.toLowerCase()) || false,
                e?.dsm_id
                  ?.toString()
                  ?.toLowerCase()
                  .includes(search.toLowerCase()) || false,
                e?.name?.toLowerCase().includes(search.toLowerCase()) || false,
                e?.type_hours?.toLowerCase().includes(search.toLowerCase()) ||
                  false,
              ];

              if (search === "") return e;

              for (let i = 0; i < data.length; i++) {
                if (data[i] === true) {
                  return e;
                }
              }

              return null;
            })}
            columns={columns.filter((e) => {
              // Garantir que sempre temos arrays
              const contractPerms = Array.isArray(contractsPermissions?.data)
                ? contractsPermissions.data
                : Array.isArray(contractsPermissions)
                  ? contractsPermissions
                  : [];

              const generalPerms = Array.isArray(permissions?.data)
                ? permissions.data
                : Array.isArray(permissions)
                  ? permissions
                  : [];

              // Combinar ambas as permissões
              const allPermissions = [...contractPerms, ...generalPerms];

              // Se não há permissões, mostrar todas as colunas (fallback para desenvolvimento)
              if (allPermissions.length === 0) {
                return true;
              }

              return allPermissions
                .map((permission) => permission.code)
                .includes(e.code);
            })}
            pagination={pagination}
          />
        </Row>
      </Modal>
    </>
  );
};
