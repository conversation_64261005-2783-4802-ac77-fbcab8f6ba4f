import { createSlice } from "@reduxjs/toolkit";
import dayjs from "dayjs";

const REDUCER_NAME = "contracts";

const allOption = { value: 0, label: "Todos" };

const INITIAL_STATE = {
  active: 1,
  search: "",
  activeContracts: [],
  inactiveContracts: [],
  permissions: [],
  executives: [],
  wallets: [],
  deactivateReasonsValues: [],
  dtEnd: null,
  dtStart: null,
  filteredContracts: [],
  contractTypes: [allOption],
  selectedContract: 0,
  selectedExecutive: null,
  selectedSquads: [allOption],
  squadOptions: [],
  selectedPool: 0,
  poolTypes: [
    { value: 0, label: "Todos" },
    { value: 1, label: "Pool 8X5" },
    { value: 2, label: "Pool setup 8X5" },
    { value: 3, label: "Pool 24X7" },
    { value: 4, label: "Pool setup 24X7" },
  ],
  deactivateReasonsValues: [],
  currentContract: {},
  users: [],
};

const contractSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setContractFieldReduce(state, action) {
      // Converter objetos dayjs/moment para strings serializáveis
      let value = action.payload.value;
      if (value && typeof value === 'object' && (value.isDayjsObject || value._isAMomentObject)) {
        value = value.format('YYYY-MM-DD');
      }
      state[action.payload.field] = value;
    },
  },
});

export const { setContractFieldReduce } = contractSlice.actions;

export default contractSlice.reducer;
