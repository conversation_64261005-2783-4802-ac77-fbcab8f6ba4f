/**
 * Testes de Integração - Fase 2: Serviços de Autenticação
 * Valida a integração entre httpOnlyAuthService e authService
 */

import { httpOnlyAuthService } from '../../services/httpOnlyAuthService';
import { authService } from '../../services/authService';
import { authConfigManager } from '../../utils/authConfig';

// Mock axios
jest.mock('axios', () => ({
  get: jest.fn(),
  post: jest.fn(),
  defaults: {},
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  }))
}));

const axios = require('axios');

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  }
}));

// Mock verifyExpTime
jest.mock('../../service/verifyExpTime', () => ({
  verifyExpTime: jest.fn()
}));

describe('Integração de Autenticação - Fase 2', () => {
  beforeEach(() => {
    // Limpar mocks
    jest.clearAllMocks();
    
    // Limpar localStorage
    localStorage.clear();
    
    // Reset authConfigManager
    authConfigManager.reset();
    
    // Reset window.__AUTH_CONFIG__
    delete window.__AUTH_CONFIG__;
  });

  describe('HttpOnlyAuthService', () => {
    test('deve inicializar com suporte a cookies HttpOnly', async () => {
      // Mock resposta do backend indicando suporte
      axios.get.mockResolvedValue({
        data: {
          auth: {
            httpOnlySupported: true
          }
        }
      });

      const config = await httpOnlyAuthService.initializeAuth();

      expect(config.auth.httpOnlySupported).toBe(true);
      expect(authConfigManager.isHttpOnlySupported()).toBe(true);
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('auth/config'),
        expect.objectContaining({
          withCredentials: true
        })
      );
    });

    test('deve fazer fallback para localStorage quando cookies não são suportados', async () => {
      // Mock resposta do backend indicando não suporte
      axios.get.mockResolvedValue({
        data: {
          auth: {
            httpOnlySupported: false
          }
        }
      });

      const config = await httpOnlyAuthService.initializeAuth();

      expect(config.auth.httpOnlySupported).toBe(false);
      expect(authConfigManager.isHttpOnlySupported()).toBe(false);
    });

    test('deve fazer fallback quando backend não responde', async () => {
      // Mock erro na requisição
      axios.get.mockRejectedValue(new Error('Network error'));

      try {
        await httpOnlyAuthService.initializeAuth();
      } catch (error) {
        expect(error.message).toBe('Network error');
      }

      expect(authConfigManager.isHttpOnlySupported()).toBe(false);
    });

    test('deve definir token via cookies HttpOnly quando suportado', async () => {
      // Configurar suporte a cookies
      axios.get.mockResolvedValue({
        data: { auth: { httpOnlySupported: true } }
      });

      await httpOnlyAuthService.initializeAuth();

      // Mock resposta de sucesso para set-token
      axios.post.mockResolvedValue({ status: 200 });

      const token = 'test-token';
      const userInfo = { name: 'Test User', email: '<EMAIL>' };

      const success = await httpOnlyAuthService.setAuthToken(token, userInfo);

      expect(success).toBe(true);
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('auth/set-token'),
        { token, userInfo },
        expect.objectContaining({ withCredentials: true })
      );

      // Verificar se dados do usuário foram salvos no localStorage
      expect(localStorage.getItem('@dsm/name')).toBe('Test User');
      expect(localStorage.getItem('@dsm/mail')).toBe('<EMAIL>');
    });

    test('deve verificar autenticação via cookies HttpOnly', async () => {
      // Configurar suporte a cookies
      axios.get.mockResolvedValueOnce({
        data: { auth: { httpOnlySupported: true } }
      });

      await httpOnlyAuthService.initializeAuth();

      // Mock resposta de sucesso para verify
      axios.get.mockResolvedValue({ status: 200 });

      const isAuth = await httpOnlyAuthService.isAuthenticated();

      expect(isAuth).toBe(true);
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('auth/verify'),
        expect.objectContaining({ withCredentials: true })
      );
    });

    test('deve fazer logout via cookies HttpOnly', async () => {
      // Configurar suporte a cookies
      axios.get.mockResolvedValue({
        data: { auth: { httpOnlySupported: true } }
      });

      await httpOnlyAuthService.initializeAuth();

      // Mock resposta de sucesso para logout
      axios.post.mockResolvedValue({ status: 200 });

      await httpOnlyAuthService.logout();

      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('auth/logout'),
        {},
        expect.objectContaining({ withCredentials: true })
      );

      // Verificar se dados locais foram limpos
      expect(localStorage.getItem('@dsm/name')).toBeNull();
      expect(localStorage.getItem('jwt')).toBeNull();
    });
  });

  describe('AuthService Integração', () => {
    test('deve usar httpOnlyAuthService quando disponível', async () => {
      // Mock httpOnlyAuthService
      const mockSetAuthToken = jest.spyOn(httpOnlyAuthService, 'setAuthToken')
        .mockResolvedValue(true);

      const token = 'test-token';
      const userInfo = { name: 'Test User', email: '<EMAIL>' };

      const success = await authService.setToken(token, userInfo);
      
      expect(success).toBe(true);
      expect(mockSetAuthToken).toHaveBeenCalledWith(token, userInfo);
    });

    test('deve fazer fallback para localStorage quando httpOnly falha', async () => {
      // Mock httpOnlyAuthService para falhar
      jest.spyOn(httpOnlyAuthService, 'setAuthToken')
        .mockRejectedValue(new Error('HttpOnly failed'));

      const token = 'test-token';
      const userInfo = { name: 'Test User', email: '<EMAIL>' };

      const success = await authService.setToken(token, userInfo);
      
      expect(success).toBe(true);
      expect(localStorage.getItem('jwt')).toBe(token);
      expect(localStorage.getItem('@dsm/name')).toBe('Test User');
    });

    test('deve verificar autenticação via httpOnlyAuthService primeiro', async () => {
      // Mock httpOnlyAuthService
      const mockIsAuthenticated = jest.spyOn(httpOnlyAuthService, 'isAuthenticated')
        .mockResolvedValue(true);

      const isAuth = await authService.isAuthenticated();
      
      expect(isAuth).toBe(true);
      expect(mockIsAuthenticated).toHaveBeenCalled();
    });

    test('deve fazer logout via httpOnlyAuthService', async () => {
      // Mock httpOnlyAuthService
      const mockLogout = jest.spyOn(httpOnlyAuthService, 'logout')
        .mockResolvedValue();

      await authService.logout();
      
      expect(mockLogout).toHaveBeenCalled();
      // Verificar se localStorage foi limpo também
      expect(localStorage.getItem('jwt')).toBeNull();
    });
  });

  describe('Migração de Dados', () => {
    test('deve migrar dados do localStorage para httpOnly', async () => {
      // Configurar dados no localStorage
      localStorage.setItem('jwt', 'existing-token');
      localStorage.setItem('@dsm/name', 'Existing User');
      localStorage.setItem('@dsm/mail', '<EMAIL>');

      // Mock httpOnlyAuthService
      const mockSetAuthToken = jest.spyOn(httpOnlyAuthService, 'setAuthToken')
        .mockResolvedValue(true);

      const migrated = await authService.migrateFromLocalStorage();
      
      expect(migrated).toBe(true);
      expect(mockSetAuthToken).toHaveBeenCalledWith(
        'existing-token',
        expect.objectContaining({
          name: 'Existing User',
          email: '<EMAIL>'
        })
      );
    });

    test('não deve migrar quando não há dados no localStorage', async () => {
      const migrated = await authService.migrateFromLocalStorage();
      
      expect(migrated).toBe(false);
    });
  });
});
